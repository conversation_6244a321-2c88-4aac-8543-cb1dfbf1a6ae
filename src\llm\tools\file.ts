import type { Folder } from 'src/types/doc';
import { useDocStore } from 'src/stores/doc';
import { useUiStore } from 'src/stores/ui';
import type { JSONContent } from '@tiptap/vue-3';
import { convertContentToTiptap } from 'src/utils/tiptap';

import { $t } from 'src/composables/useTrans';

interface OperationResult {
  success: boolean;
  message: string;
  operation?: string;
  searchText?: string;
  newContent?: string;
  matchInfo?: {
    position: number;
    originalText: string;
    context: string;
  };
  verification?: {
    verified: boolean;
    message: string;
    details?: string;
  };
}

/**
 * 获取当前文件树结构
 */
export async function getFileTree(): Promise<{
  success: boolean;
  message: string;
  folders?: Array<{
    id: number;
    name: string;
    parent_id: number | null;
  }>;
  documents?: Array<{
    id: number;
    name: string;
    parent_folder_id: number;
  }>;
}> {
  try {
    const { useSqlite } = await import('src/composeables/useSqlite');

    const folders: Array<{
      id: number;
      name: string;
      parent_id: number | null;
    }> = [];

    const documents: Array<{
      id: number;
      name: string;
      parent_folder_id: number;
    }> = [];

    // 递归获取文件夹和文档的函数
    const collectFolderData = async (parentId: number | null) => {
      try {
        // 获取指定父文件夹下的所有子文件夹
        const subFolders = await useSqlite().listFolders(parentId || -1);

        for (const folder of subFolders) {
          // 添加文件夹信息
          folders.push({
            id: folder.id,
            name: folder.name || $t('src.llm.tools.file.unnamedFolder'),
            parent_id: folder.parent_id,
          });

          // 获取该文件夹下的所有文档
          try {
            const documentIds = await useSqlite().getAllDocumentsInFolder(folder.id);
            if (documentIds && documentIds.length > 0) {
              for (const docId of documentIds) {
                try {
                  const doc = await useSqlite().getDocument(docId);
                  documents.push({
                    id: doc.id,
                    name: doc.title || $t('src.llm.tools.file.unnamedDocument'),
                    parent_folder_id: folder.id,
                  });
                } catch (docError) {
                  console.warn(`获取文档 ${docId} 详情失败:`, docError);
                }
              }
            }
          } catch (error) {
            console.warn(`获取文件夹 ${folder.id} 的文档失败:`, error);
          }

          // 递归获取子文件夹的内容
          await collectFolderData(folder.id);
        }
      } catch (error) {
        console.warn(`获取文件夹 ${parentId} 的子文件夹失败:`, error);
      }
    };

    // 从根文件夹开始递归获取所有数据
    await collectFolderData(null);

    // 构建完整的树结构数据
    const treeStructure = {
      folders: folders,
      documents: documents,
      summary: {
        totalFolders: folders.length,
        totalDocuments: documents.length,
        foldersByParent: folders.reduce(
          (acc, folder) => {
            const parentKey = folder.parent_id?.toString() || 'root';
            if (!acc[parentKey]) acc[parentKey] = [];
            acc[parentKey].push({
              id: folder.id,
              name: folder.name,
            });
            return acc;
          },
          {} as Record<string, Array<{ id: number; name: string }>>,
        ),
        documentsByFolder: documents.reduce(
          (acc, doc) => {
            const folderKey = doc.parent_folder_id.toString();
            if (!acc[folderKey]) acc[folderKey] = [];
            acc[folderKey].push({
              id: doc.id,
              name: doc.name,
            });
            return acc;
          },
          {} as Record<string, Array<{ id: number; name: string }>>,
        ),
      },
    };

    return {
      success: true,
      message: $t('src.llm.tools.file.getFileTree.success', {
        folderCount: folders.length,
        documentCount: documents.length,
        detail: JSON.stringify(treeStructure, null, 2),
      }),
      folders,
      documents,
    };
  } catch (error) {
    console.error('获取文件树失败:', error);
    return {
      success: false,
      message: $t('src.llm.tools.file.getFileTree.failed', {
        error: error instanceof Error ? error.message : String(error),
      }),
    };
  }
}

/**
 * 创建新文档
 */
export async function createDocument(params: {
  title: string;
  folderId: number;
  content?: string;
  openAfterCreate?: boolean;
}): Promise<OperationResult> {
  const { title, folderId, content = '', openAfterCreate = true } = params;

  if (!title || title.trim().length === 0) {
    return {
      success: false,
      message: $t('src.llm.tools.file.createDocument.emptyTitle'),
    };
  }

  try {
    const { useSqlite } = await import('src/composeables/useSqlite');

    let folder: Folder;
    if (!folderId || folderId !== -1) {
      // 检查文件夹是否存在
      try {
        folder = await useSqlite().getFolder(folderId);
      } catch {
        return {
          success: false,
          message: $t('src.llm.tools.file.createDocument.folderNotFound', { folderId }),
        };
      }
    }

    const docStore = useDocStore();

    // 准备文档内容
    let documentContent = docStore.tiptapEmptyContent;
    if (content && content.trim().length > 0) {
      // 检查内容是否为Markdown格式，如果是则转换为TipTap JSON格式
      documentContent = convertContentToTiptap(content);
    }

    // 创建文档
    const docId = await useSqlite().createDocument(title, documentContent, folderId);
    const newDoc = await useSqlite().getDocument(docId);

    // 更新store中的文件树
    void docStore.addDocumentToTree(newDoc, folderId);

    // 如果需要，打开新创建的文档
    if (openAfterCreate) {
      const { useDocumentActions } = await import('src/composeables/useDocumentActions');
      const { onOpenDocument } = useDocumentActions();
      onOpenDocument(newDoc, folderId);
    }

    console.log(`AI创建文档成功: ${title}`);

    return {
      success: true,
      message: $t('src.llm.tools.file.createDocument.success', { title, folderName: folder.name }),
      operation: 'create',
      newContent: title,
    };
  } catch (error) {
    console.error('AI创建文档失败:', error);
    return {
      success: false,
      message: $t('src.llm.tools.file.createDocument.error', {
        error: error instanceof Error ? error.message : String(error),
      }),
    };
  }
}

/**
 * 批量删除文档
 */
export async function deleteDocuments(params: { documentIds: number[] }): Promise<OperationResult> {
  const { documentIds } = params;

  if (!documentIds || documentIds.length === 0) {
    return {
      success: false,
      message: $t('src.llm.tools.file.deleteDocuments.emptyIds'),
    };
  }

  if (!Array.isArray(documentIds)) {
    return {
      success: false,
      message: $t('src.llm.tools.file.deleteDocuments.idsNotArray'),
    };
  }

  try {
    const { useSqlite } = await import('src/composeables/useSqlite');
    const docStore = useDocStore();
    const deleteResults: Array<{ id: number; success: boolean; error?: string }> = [];

    for (const docId of documentIds) {
      try {
        // 从所有窗口中移除该文档
        for (const window of docStore.splitterWindows) {
          const docIndex = window.documents?.findIndex((d: { id: number }) => d.id === docId);
          if (docIndex !== undefined && docIndex !== -1) {
            docStore.removeDocument(window.id, docId);
          }
        }

        // 从数据库删除
        await useSqlite().deleteDocument(docId);

        // 从store中的文件树移除
        docStore.removeDocumentFromTree(docId);

        deleteResults.push({
          id: docId,
          success: true,
        });
      } catch (error) {
        deleteResults.push({
          id: docId,
          success: false,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }
    return {
      success: true,
      message: $t('src.llm.tools.file.deleteDocuments.success'),
    };
  } catch (error) {
    console.error('AI批量删除文档失败:', error);
    return {
      success: false,
      message: $t('src.llm.tools.file.deleteDocuments.error', {
        error: error instanceof Error ? error.message : String(error),
      }),
    };
  }
}

/**
 * 重命名文档
 */
export async function renameDocument(params: {
  documentId: number;
  newTitle: string;
  currentTitle?: string;
}): Promise<OperationResult> {
  const { documentId, newTitle, currentTitle } = params;

  if (!newTitle || newTitle.trim().length === 0) {
    return {
      success: false,
      message: $t('src.llm.tools.file.renameDocument.emptyTitle'),
    };
  }

  try {
    const { useSqlite } = await import('src/composeables/useSqlite');
    const docStore = useDocStore();

    // 获取当前文档标题（如果没有提供）
    let oldTitle = currentTitle;
    try {
      const document = await useSqlite().getDocument(documentId);
      oldTitle = document.title || `文档${documentId}`;
    } catch {
      return {
        success: false,
        message: $t('src.llm.tools.file.renameDocument.notFound', { documentId }),
      };
    }

    if (oldTitle === newTitle.trim()) {
      return {
        success: false,
        message: $t('src.llm.tools.file.renameDocument.sameName'),
      };
    }

    // 使用新的后端方法重命名文档
    await useSqlite().renameDocument(documentId, newTitle.trim());

    // 获取更新后的文档信息
    const updatedDoc = await useSqlite().getDocument(documentId);

    // 更新store中的文件树
    docStore.updateDocumentInTree(updatedDoc);

    // 同步所有窗口中的文档信息
    docStore.syncDocuments(updatedDoc);

    console.log(`AI重命名文档成功: "${oldTitle}" -> "${newTitle}"`);

    return {
      success: true,
      message: $t('src.llm.tools.file.renameDocument.success', {
        oldTitle,
        newTitle: newTitle.trim(),
      }),
      operation: 'rename',
      searchText: oldTitle,
      newContent: newTitle.trim(),
    };
  } catch (error) {
    console.error('AI重命名文档失败:', error);
    return {
      success: false,
      message: $t('src.llm.tools.file.renameDocument.error', {
        error: error instanceof Error ? error.message : String(error),
      }),
    };
  }
}

/**
 * 搜索文档
 */
export async function searchDocuments(params: {
  searchText: string;
  searchInContent?: boolean;
  folderId?: number;
}): Promise<{
  success: boolean;
  message: string;
  results?: Array<{
    id: number;
    title: string;
    folderId: number;
    folderName: string;
    matchType: 'title' | 'content';
    preview?: string;
  }>;
}> {
  const { searchText, searchInContent = false, folderId } = params;

  if (!searchText || searchText.trim().length === 0) {
    return {
      success: false,
      message: $t('src.llm.tools.file.searchDocuments.emptyText'),
    };
  }

  try {
    const { useSqlite } = await import('src/composeables/useSqlite');
    const result = await useSqlite().searchDocuments(searchText, searchInContent, folderId);

    if (result.success && result.data) {
      console.log(`AI搜索文档成功: 找到 ${result.data.length} 个匹配结果`);

      // 转换数据格式以匹配返回类型
      const formattedResults = result.data.map((doc) => ({
        id: doc.id,
        title: doc.title,
        folderId: doc.folder_id || 0, // 如果没有文件夹ID，使用0作为默认值
        folderName: doc.folder_name || '未知文件夹',
        matchType: doc.match_type,
        preview: doc.preview,
      }));
      const details = formattedResults.map((i, idx) => {
        return `${idx + 1}: id - ${i.id}, title - ${i.title}, folderId - ${i.folderId}, folderName - ${i.folderName}, matchType - ${i.matchType}`;
      });
      console.log('details', details);

      return {
        success: true,
        message: $t('src.llm.tools.file.searchDocuments.success', {
          searchText,
          count: result.data.length,
          details: JSON.stringify(details),
        }),
        results: formattedResults,
      };
    } else {
      console.error('AI搜索文档失败:', result.message);
      return {
        success: false,
        message:
          result.message ||
          $t('src.llm.tools.file.searchDocuments.error', {
            error: '搜索失败',
          }),
      };
    }
  } catch (error) {
    console.error('AI搜索文档时发生错误:', error);
    return {
      success: false,
      message: $t('src.llm.tools.file.searchDocuments.error', {
        error: error instanceof Error ? error.message : String(error),
      }),
    };
  }
}

/**
 * 创建新文件夹
 */
export function createFolder(params: { name: string; parentId?: number | null }): OperationResult {
  // const { t: $t } = useTrans();
  const { name, parentId = null } = params;

  if (!name || name.trim().length === 0) {
    return {
      success: false,
      message: $t('src.llm.tools.file.createFolder.emptyName'),
    };
  }

  const docStore = useDocStore();

  try {
    let folderId: number | null = null;
    let newFolder: Folder | null = null;
    // 异步操作
    void (async () => {
      try {
        const { useSqlite } = await import('src/composeables/useSqlite');

        // 如果指定了父文件夹，检查是否存在
        if (parentId !== null && parentId > 0) {
          const parentFolder = await useSqlite().getFolder(parentId);
          if (!parentFolder) {
            return {
              success: false,
              message: $t('src.llm.tools.file.createFolder.parentNotFound', { parentId }),
            };
          }
        }

        // 创建文件夹
        folderId = await useSqlite().createFolder(name.trim(), parentId);
        newFolder = await useSqlite().getFolder(folderId);

        // 更新store中的文件树
        void docStore.addFolderToTree(newFolder, parentId);

        console.log(`AI创建文件夹成功: ${name} (ID: ${folderId})`);
      } catch (error) {
        console.error('AI创建文件夹失败:', error);
      }
    })();

    const parentName = parentId
      ? docStore.folderMap.get(parentId)?.name || `文件夹${parentId}`
      : '根目录';

    return {
      success: true,
      message: $t('src.llm.tools.file.createFolder.success', {
        name,
        parentName,
        parentId,
        folderId,
      }),
      operation: 'create',
      newContent: name,
    };
  } catch (error) {
    return {
      success: false,
      message: $t('src.llm.tools.file.createFolder.error', {
        error: error instanceof Error ? error.message : String(error),
      }),
    };
  }
}

/**
 * 删除文件夹
 */
export async function deleteFolder(params: {
  folderId: number;
  folderName?: string;
}): Promise<OperationResult> {
  const { folderId, folderName } = params;

  try {
    const { useSqlite } = await import('src/composeables/useSqlite');
    const docStore = useDocStore();

    // 从后端获取文件夹信息
    let folder: Folder;
    try {
      folder = await useSqlite().getFolder(folderId);
    } catch {
      return {
        success: false,
        message: $t('src.llm.tools.file.deleteFolder.notFound', { folderId }),
      };
    }

    const targetFolderName = folderName || folder.name || `文件夹${folderId}`;

    // 从后端检查是否有子文件夹
    const subFolders = await useSqlite().listFolders(folderId);
    const hasChildren = subFolders && subFolders.length > 0;

    // 从后端检查是否有文档
    const documentIds = await useSqlite().getAllDocumentsInFolder(folderId);
    const hasDocuments = documentIds && documentIds.length > 0;

    if (hasChildren || hasDocuments) {
      const childCount = (subFolders?.length || 0) + (documentIds?.length || 0);
      return {
        success: false,
        message: $t('src.llm.tools.file.deleteFolder.hasChildren', {
          folderName: targetFolderName,
          count: childCount,
        }),
      };
    }

    // 从数据库删除文件夹
    await useSqlite().deleteFolder(folderId);

    // 从store中的文件树移除
    docStore.removeFolderFromTree(folderId);

    console.log(`AI删除文件夹成功: ${targetFolderName}`);

    return {
      success: true,
      message: $t('src.llm.tools.file.deleteFolder.success', { folderName: targetFolderName }),
      operation: 'delete',
      searchText: targetFolderName,
    };
  } catch (error) {
    console.error('AI删除文件夹失败:', error);
    return {
      success: false,
      message: $t('src.llm.tools.file.deleteFolder.error', {
        error: error instanceof Error ? error.message : String(error),
      }),
    };
  }
}

/**
 * 重命名文件夹
 */
export async function renameFolder(params: {
  folderId: number;
  newName: string;
  currentName?: string;
}): Promise<OperationResult> {
  const { folderId, newName, currentName } = params;

  if (!newName || newName.trim().length === 0) {
    return {
      success: false,
      message: $t('src.llm.tools.file.renameFolder.emptyName'),
    };
  }

  try {
    const { useSqlite } = await import('src/composeables/useSqlite');
    const docStore = useDocStore();

    // 从后端获取文件夹信息
    let folder: Folder;
    try {
      folder = await useSqlite().getFolder(folderId);
    } catch {
      return {
        success: false,
        message: $t('src.llm.tools.file.renameFolder.notFound', { folderId }),
      };
    }

    const oldName = currentName || folder.name || `文件夹${folderId}`;

    if (oldName === newName.trim()) {
      return {
        success: false,
        message: $t('src.llm.tools.file.renameFolder.sameName'),
      };
    }

    // 重命名文件夹
    await useSqlite().updateFolder(folderId, newName.trim(), folder.parent_id);
    const updatedFolder = await useSqlite().getFolder(folderId);

    // 更新store中的文件树
    docStore.updateFolderInTreeData(updatedFolder);

    console.log(`AI重命名文件夹成功: "${oldName}" -> "${newName}"`);

    return {
      success: true,
      message: $t('src.llm.tools.file.renameFolder.success', { oldName, newName }),
      operation: 'rename',
      searchText: oldName,
      newContent: newName,
    };
  } catch (error) {
    console.error('AI重命名文件夹失败:', error);
    return {
      success: false,
      message: $t('src.llm.tools.file.renameFolder.error', {
        error: error instanceof Error ? error.message : String(error),
      }),
    };
  }
}

/**
 * 搜索文件夹
 */
export async function searchFolders(params: { keyword: string }): Promise<{
  success: boolean;
  message: string;
  results?: Array<{
    id: number;
    name: string;
    parent_id: number | null;
    parent_name?: string;
    document_count: number;
  }>;
}> {
  const { keyword } = params;

  if (!keyword || keyword.trim().length === 0) {
    return {
      success: false,
      message: $t('src.llm.tools.file.searchFolders.emptyKeyword'),
    };
  }

  try {
    const { useSqlite } = await import('src/composeables/useSqlite');
    const result = await useSqlite().searchFolders(keyword);

    if (result.success && result.data) {
      console.log(`AI搜索文件夹成功: 找到 ${result.data.length} 个匹配结果`);

      return {
        success: true,
        message: $t('src.llm.tools.file.searchFolders.success', {
          keyword,
          count: result.data.length,
          details: `找到 ${result.data.length} 个包含 "${keyword}" 的文件夹`,
        }),
        results: result.data,
      };
    } else {
      console.error('AI搜索文件夹失败:', result.message);
      return {
        success: false,
        message:
          result.message ||
          $t('src.llm.tools.file.searchFolders.error', {
            error: '搜索失败',
          }),
      };
    }
  } catch (error) {
    console.error('AI搜索文件夹时发生错误:', error);
    return {
      success: false,
      message: $t('src.llm.tools.file.searchFolders.error', {
        error: error instanceof Error ? error.message : String(error),
      }),
    };
  }
}

/**
 * 打开文档
 */
export async function openDocument(params: { documentId: number }): Promise<OperationResult> {
  // console.log('openDocument', params);

  const { documentId } = params;
  if (!documentId) {
    return {
      success: false,
      message: $t('src.llm.tools.file.openDocument.error.noDocumentId'),
    };
  }

  try {
    const { useSqlite } = await import('src/composeables/useSqlite');
    const { useDocumentActions } = await import('src/composeables/useDocumentActions');

    // 直接从后端获取文档信息
    const document = await useSqlite().getDocument(documentId);
    console.log('📋 [useDocumentActions] 获取文档信息:', document);

    if (!document) {
      console.error(`文档 ${documentId} 不存在`);
      return {
        success: false,
        message: $t('src.llm.tools.file.openDocument.notFound', { documentId }),
      };
    }

    const folderId = document.folder_id;
    if (!folderId) {
      console.error(`文档 ${documentId} 没有关联的文件夹`);
      return {
        success: false,
        message: $t('src.llm.tools.file.openDocument.noFolder', { documentId }),
      };
    }

    // 递归展开文件夹路径，确保从根目录到目标文件夹的所有父文件夹都被展开
    await expandFolderPath(folderId);

    // 调用打开文档事件
    const { onOpenDocument } = useDocumentActions();
    onOpenDocument(document, folderId);

    const uiStore = useUiStore();
    uiStore.highlightTreeItem = `document-${document.id}`;

    console.log(`AI打开文档成功: ${document.title}`);

    return {
      success: true,
      message: $t('src.llm.tools.file.openDocument.success', {
        documentId: document.id,
        title: document.title,
      }),
    };
  } catch (error) {
    console.error('AI打开文档失败:', error);
    return {
      success: false,
      message: $t('src.llm.tools.file.openDocument.error', {
        error: error instanceof Error ? error.message : String(error),
      }),
    };
  }
}

/**
 * 递归展开文件夹路径，从根目录到目标文件夹的所有父文件夹
 */
async function expandFolderPath(targetFolderId: number): Promise<void> {
  const { useSqlite } = await import('src/composeables/useSqlite');
  const { useUiStore } = await import('src/stores/ui');
  const uiStore = useUiStore();

  // 构建从目标文件夹到根目录的路径
  const path: number[] = [];
  let currentFolderId: number | null = targetFolderId;

  // 向上遍历找到所有父文件夹，使用后端API获取文件夹信息
  while (currentFolderId !== null && currentFolderId !== -1) {
    try {
      const folder = await useSqlite().getFolder(currentFolderId);
      path.unshift(currentFolderId);

      // 如果parent_id为null、-1或0，表示到达根目录
      if (!folder.parent_id || folder.parent_id === -1 || folder.parent_id === 0) {
        currentFolderId = null;
      } else {
        currentFolderId = folder.parent_id;
      }
    } catch (error) {
      console.error(`获取文件夹 ${currentFolderId} 信息失败:`, error);
      // 如果文件夹不存在，停止向上遍历
      break;
    }
  }

  // 如果路径为空，说明目标文件夹不存在
  if (path.length === 0) {
    console.warn(`文件夹 ${targetFolderId} 不存在，无法展开路径`);
    return;
  }

  // 使用 UI Store 的 triggerFolderExpansion 方法来展开文件夹路径
  console.log(`触发文件夹展开路径: ${path.join(' -> ')}`);
  uiStore.triggerFolderExpansion(path);

  // 给UI一些时间来处理展开操作
  await new Promise((resolve) => setTimeout(resolve, 200));

  console.log(`已构建文件夹展开路径: ${path.join(' -> ')}`);
}

/**
 * 修改文档内容
 */
export function updateDocumentContent(params: {
  documentId: number;
  content: string;
  documentName?: string;
}): OperationResult {
  // const { t: $t } = useTrans();
  const { documentId, content } = params;

  const docStore = useDocStore();

  try {
    // 异步操作
    void (async () => {
      try {
        const { useSqlite } = await import('src/composeables/useSqlite');

        // 获取现有文档信息
        const existingDoc = await useSqlite().getDocument(documentId);

        // 准备新的文档内容
        let documentContent: JSONContent;
        if (content.trim().length === 0) {
          // 空内容使用默认空结构
          documentContent = docStore.tiptapEmptyContent;
        } else {
          // 检查内容是否为Markdown格式，如果是则转换为TipTap JSON格式
          documentContent = convertContentToTiptap(content);
        }

        // 更新文档，保持原有的title、folder_id和metadata
        await useSqlite().updateDocument(
          documentId,
          existingDoc.title,
          documentContent,
          existingDoc.folder_id,
          existingDoc.metadata || '{}',
        );

        // 获取更新后的文档
        const updatedDoc = await useSqlite().getDocument(documentId);

        // 更新store中的文档信息
        docStore.updateDocumentInTree(updatedDoc);

        // 同步所有窗口中的文档内容
        docStore.syncDocuments(updatedDoc);

        // 检查文档是否已经在编辑器中打开，如果是则同步更新编辑器内容
        const windowContainingDoc = docStore.getWindowContainingDocument(documentId);
        if (windowContainingDoc) {
          const editorKey = docStore.generateEditorInstanceKey(windowContainingDoc.id, documentId);
          const editorInstance = docStore.getEditorInstanceByKey(editorKey);

          if (editorInstance) {
            // 更新编辑器内容
            editorInstance.commands.setContent(documentContent);

            // 同步更新documentContents中的内容
            docStore.updateEditorContentV2(documentId, documentContent);
          }
        }
      } catch (error) {
        console.error('AI修改文档内容失败:', error);
      }
    })();

    return {
      success: true,
      message: $t('src.llm.tools.file.updateDocumentContent.success'),
      operation: 'update',
      newContent: content.length > 100 ? `${content.substring(0, 100)}...` : content,
    };
  } catch (error) {
    return {
      success: false,
      message: $t('src.llm.tools.file.updateDocumentContent.error', {
        error: error instanceof Error ? error.message : String(error),
      }),
    };
  }
}

// 导出所有工具函数
export const fileTools = {
  getFileTree,
  searchFolders,
  createDocument,
  deleteDocuments,
  renameDocument,
  searchDocuments,
  createFolder,
  deleteFolder,
  renameFolder,
  openDocument,
  updateDocumentContent,
};

// 文件工具schema定义
export const tools = [
  {
    type: 'function' as const,
    function: {
      name: 'get_file_tree',
      description: '获取当前文件树结构，返回所有文件夹和文档的扁平化列表，包含id、名称和父级关系',
      parameters: {
        type: 'object',
        properties: {},
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'search_folders',
      description:
        '根据关键词搜索文件夹，返回包含关键字的文件夹数组，包含文件夹的id、名称、父级信息和文档数量',
      parameters: {
        type: 'object',
        properties: {
          keyword: {
            type: 'string',
            description: '搜索关键词，用于模糊匹配文件夹名称',
          },
        },
        required: ['keyword'],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'create_document',
      description: '在指定文件夹中创建新文档',
      parameters: {
        type: 'object',
        properties: {
          title: {
            type: 'string',
            description: '文档标题',
          },
          folderId: {
            type: 'number',
            description: '目标文件夹的ID',
          },
          content: {
            type: 'string',
            description: '文档内容（可选）',
          },
          openAfterCreate: {
            type: 'boolean',
            description: '创建后是否打开文档（默认true）',
          },
        },
        required: ['title', 'folderId'],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'delete_documents',
      description: '批量删除多个文档',
      parameters: {
        type: 'object',
        properties: {
          documentIds: {
            type: 'array',
            items: {
              type: 'number',
            },
            description: '要删除的文档ID数组',
          },
        },
        required: ['documentIds'],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'rename_document',
      description: '重命名指定的文档',
      parameters: {
        type: 'object',
        properties: {
          documentId: {
            type: 'number',
            description: '要重命名的文档ID',
          },
          newTitle: {
            type: 'string',
            description: '新的文档标题',
          },
          currentTitle: {
            type: 'string',
            description: '当前文档标题（可选）',
          },
        },
        required: ['documentId', 'newTitle'],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'search_documents',
      description: '在文档中搜索指定的文本内容',
      parameters: {
        type: 'object',
        properties: {
          searchText: {
            type: 'string',
            description: '要搜索的文本',
          },
          searchInContent: {
            type: 'boolean',
            description: '是否在文档内容中搜索（默认false，仅搜索标题）',
          },
          folderId: {
            type: 'number',
            description: '限制搜索的文件夹ID（可选）',
          },
        },
        required: ['searchText'],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'create_folder',
      description: '创建新文件夹',
      parameters: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            description: '文件夹名称',
          },
          parentId: {
            type: 'number',
            description: '父文件夹ID（可选，null表示根目录）',
          },
        },
        required: ['name'],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'delete_folder',
      description: '删除指定的文件夹（仅当文件夹为空时）',
      parameters: {
        type: 'object',
        properties: {
          folderId: {
            type: 'number',
            description: '要删除的文件夹ID',
          },
          folderName: {
            type: 'string',
            description: '文件夹名称（可选，用于确认）',
          },
        },
        required: ['folderId'],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'rename_folder',
      description: '重命名指定的文件夹',
      parameters: {
        type: 'object',
        properties: {
          folderId: {
            type: 'number',
            description: '要重命名的文件夹ID',
          },
          newName: {
            type: 'string',
            description: '新的文件夹名称',
          },
          currentName: {
            type: 'string',
            description: '当前文件夹名称（可选）',
          },
        },
        required: ['folderId', 'newName'],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'open_document',
      description: '打开指定的文档，只需要提供文档ID，系统会自动查找文件位置并展开文件夹树',
      parameters: {
        type: 'object',
        properties: {
          documentId: {
            type: 'number',
            description: '要打开的文档ID',
          },
        },
        required: ['documentId'],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'update_document_content',
      description: '修改指定文档的内容，保持文档标题、位置等其他信息不变',
      parameters: {
        type: 'object',
        properties: {
          documentId: {
            type: 'number',
            description: '要修改内容的文档ID',
          },
          content: {
            type: 'string',
            description: '新的文档内容（纯文本格式）',
          },
          documentName: {
            type: 'string',
            description: '文档名称（可选，用于确认）',
          },
        },
        required: ['documentId', 'content'],
      },
    },
  },
];

// 文件工具映射表
export const fileToolMappings = {
  get_file_tree: getFileTree,
  search_folders: searchFolders,
  create_document: createDocument,
  delete_documents: deleteDocuments,
  rename_document: renameDocument,
  search_documents: searchDocuments,
  create_folder: createFolder,
  delete_folder: deleteFolder,
  rename_folder: renameFolder,
  open_document: openDocument,
  update_document_content: updateDocumentContent,
} as const;
