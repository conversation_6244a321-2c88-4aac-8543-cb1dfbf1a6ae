export default {
  base: 'App Settings',
  editor: 'Editor',
  llm: 'LLM Provider',
  searchEngine: 'Search Engine',
  resourceProvider: 'Resource Provider',
  knowledgeBase: 'Knowledge Base',
  settings: {
    title: 'Settings',
    applicationSettings: 'Application Settings',
    interfaceLanguage: 'Interface Language',
    selectLanguage: 'Select Language',
    languageDescription:
      'Change the display language of the interface, settings will be saved automatically',
    languageSaved: 'Language settings saved',
    languageSaveFailed: 'Failed to save language settings',
    selectTheme: 'Select Theme',
  },
  theme_light: 'Light',
  theme_dark: 'Dark',
  fontSize: 'Font Size',
  lineHeight: 'Line Height',
  enableAutoComplete: 'Enable Auto Complete',
  autoComplete: 'Auto Complete',
  times: 'times',

  newFolder: 'New Folder',
  newDocument: 'New Document',

  appInitializing: 'Initializing App...',
  qtConnecting: 'Connecting to Qt Environment...',
  qtApiInitializing: 'Initializing Qt API...',
  initCompleted: 'Initialization Completed',
  appLoading: 'Loading App...',
  initField: 'Initialization Failed, Loading App...',
  enableEmbeddTitle: 'Enable Embedd Title',
  enableToolbar: 'Enable Toolbar',
  toolbar: 'Toolbar',

  show: 'Show',
  hide: 'Hide',

  copiedItem: 'Copy',
  configuration: 'Configuration',
  formate: 'Format',
  copySuccess: 'Copy succeeded',
  copyFailed: 'Copy failed',

  minimax: 'Hailuo AI',
  iKnownIt: 'Got it',
  ollama: 'Ollama',
  openai: 'OpenAI',
  doubao: 'Doubao LLM',

  default: 'Default',
  dont_touch_ollama_api_key: 'dont config ApiKey of Ollama',

  src: {
    components: {
      // Tiptap Editor Components
      tiptap: {
        knowledgeSearch: 'Knowledge Base',
        pexelsIntegration: 'Media Library',
        // Editor Toolbar
        editorToolbar: {
          bold: 'Bold',
          italic: 'Italic',
          underline: 'Underline',
          strikethrough: 'Strikethrough',
          code: 'Code',
          codeBlock: 'Code Block',
          paragraph: 'Paragraph',
          heading1: 'Heading 1',
          heading2: 'Heading 2',
          heading3: 'Heading 3',
          heading4: 'Heading 4',
          heading5: 'Heading 5',
          heading6: 'Heading 6',
          bulletList: 'Bullet List',
          orderedList: 'Ordered List',
          taskList: 'Task List',
          blockquote: 'Blockquote',
          horizontalRule: 'Horizontal Rule',
          hardBreak: 'Hard Break',
          alignLeft: 'Align Left',
          alignCenter: 'Align Center',
          alignRight: 'Align Right',
          alignJustify: 'Justify',
          undo: 'Undo',
          redo: 'Redo',
          link: 'Link',
          image: 'Image',
          table: 'Table',
          emoji: 'Emoji',
          highlight: 'Highlight',
          color: 'Color',
          fontSize: 'Font Size',
          fontFamily: 'Font Family',
          clearFormat: 'Clear Format',
          ai: 'AI Assistant',
          replace: 'Replace',
          find: 'Find',
          wordCount: 'Word Count',
          textColor: 'Text Color',
          backgroundColor: 'Background Color',
          addColumnBefore: 'Insert Column Left',
          addColumnAfter: 'Insert Column Right',
          deleteColumn: 'Delete Column',
          addRowBefore: 'Insert Row Above',
          addRowAfter: 'Insert Row Below',
          deleteRow: 'Delete Row',
          deleteTable: 'Delete Table',
          mergeCells: 'Merge Cells',
          splitCell: 'Split Cell',
          toggleHeaderColumn: 'Toggle Header Column',
          toggleHeaderRow: 'Toggle Header Row',
          toggleHeaderCell: 'Toggle Header Cell',
        },
        wordCountStats: {
          title: 'Document Statistics',
          selectDocument: 'Please select a document to view statistics',
          wordCount: 'Word Count',
          characterCount: 'Character Count',
          paragraphCount: 'Paragraph Count',
          readingTime: 'Reading Time (minutes)',
          documentId: 'Document ID',
          activePanel: 'Active Panel',
          lastUpdated: 'Last Updated',
        },
        documentMetadata: {
          title: 'Document Info',
          metadata: 'Metadata',
          properties: 'Properties',
          tags: 'Tags',
          category: 'Category',
          createdAt: 'Created At',
          updatedAt: 'Updated At',
          wordCount: 'Word Count',
          readingTime: 'Reading Time',
          keywords: 'Keywords',
          description: 'Description',
          addTag: 'Add Tag',
          addKeyword: 'Add Keyword',
          selectDocument: 'Please select a document to view info',
          loading: 'Loading...',
          documentId: 'Document ID',
          noTitle: 'Untitled',
          prompt: 'Prompt',
          note: 'Note',
          promptHint:
            'Prompt for AI generation to help the model better understand the document content',
          noteHint: 'Additional notes or remarks for the document',
          saving: 'Saving...',
          metadataSaved: 'Metadata Saved',
          saveMetadataFailed: 'Failed to Save Metadata',
          parseMetadataFailed: 'Failed to Parse Metadata',
          loadFailed: 'Failed to Load',
          unknown: 'Unknown',
          locale: 'en-US',
        },
        searchReplace: {
          title: 'Find & Replace',
          search: 'Find',
          replace: 'Replace',
          replaceAll: 'Replace All',
          caseSensitive: 'Case Sensitive',
          wholeWord: 'Whole Word',
          useRegex: 'Use Regex',
          noResults: 'No matches found',
          results: '{count} matches found',
          confirmReplaceAll: 'Confirm replace all matches?',
          replaceSuccess: '{count} matches replaced successfully',
        },
        translationTool: {
          title: 'Translation Tool',
          translate: 'Translate',
          sourceLanguage: 'Source Language',
          targetLanguage: 'Target Language',
          autoDetect: 'Auto Detect',
          copyResult: 'Copy Result',
          insertToEditor: 'Insert to Editor',
          replaceSelection: 'Replace Selection',
          translating: 'Translating...',
          translationComplete: 'Translation Complete',
          translationFailed: 'Translation failed, please try again',
          originalContent: 'Original Content',
          enterContentToTranslate: 'Please enter content to translate...',
          domainHint: 'Domain Hint (optional)',
          domainHintPlaceholder: 'e.g. technical docs, legal files, medical literature, etc.',
          domainHintTooltip: 'Providing domain info can improve translation quality',
          targetLanguageLabel: 'Target Language',
          startTranslation: 'Start Translation',
          translationResults: 'Translation Results:',
          clickOptionToCopy: 'Click any option to copy to clipboard',
          copy: 'Copy',
          enterContent: 'Please enter content to translate',
          selectTargetLanguage: 'Please select target language',
          configureApiKey: 'Please configure Qwen API Key in settings first',
          translationFailedGeneral: 'Translation failed, please check network and API config',
          translationRequestFailed: 'Translation request failed',
          translationResponseInvalid: 'Invalid translation response format',
          translationSuccessful: 'Translation Complete',
          copySuccessful: 'Copy succeeded',
          copyFailed: 'Copy failed',
          languages: {
            english: 'English',
            chinese: 'Chinese',
            japanese: 'Japanese',
            korean: 'Korean',
            french: 'French',
            german: 'German',
            spanish: 'Spanish',
            arabic: 'Arabic',
            russian: 'Russian',
            italian: 'Italian',
            portuguese: 'Portuguese',
            dutch: 'Dutch',
            thai: 'Thai',
            vietnamese: 'Vietnamese',
            indonesian: 'Indonesian',
            turkish: 'Turkish',
            polish: 'Polish',
            czech: 'Czech',
            hungarian: 'Hungarian',
            swedish: 'Swedish',
            finnish: 'Finnish',
            hebrew: 'Hebrew',
            hindi: 'Hindi',
            bengali: 'Bengali',
            malay: 'Malay',
            cantonese: 'Cantonese',
          },
        },
        codeBlock: {
          language: 'Language',
          copyCode: 'Copy Code',
          copied: 'Copied',
          runCode: 'Run Code',
          editCode: 'Edit Code',
          deleteCodeBlock: 'Delete Code Block',
          copyFailed: 'Copy failed',
        },
        excalidraw: {
          title: 'Drawing Tool',
          drawing: 'Drawing',
          save: 'Save Drawing',
          cancel: 'Cancel',
          clear: 'Clear Canvas',
          export: 'Export Image',
          import: 'Import File',
        },
        mermaid: {
          title: 'Chart Tool',
          diagram: 'Diagram',
          code: 'Code',
          preview: 'Preview',
          save: 'Save Chart',
          cancel: 'Cancel',
          edit: 'Edit Code',
          undo: 'Undo',
          redo: 'Redo',
          viewLargeImage: 'View Large Image',
          copy: 'Copy',
          enterCode: 'Enter Mermaid code...',
          renderError: 'Render Error',
          renderingChart: 'Rendering Chart...',
          codeCopied: 'Code Copied',
          chartCopied: 'Chart Copied',
          copyFailed: 'Copy failed',
          failedToLoadMermaid: 'Failed to load Mermaid library',
          renderFailed: 'Render failed',
          largePreview: 'Mermaid Chart Preview',
          types: {
            flowchart: 'Flowchart',
            sequence: 'Sequence Diagram',
            class: 'Class Diagram',
            state: 'State Diagram',
            entity: 'Entity Relationship Diagram',
            userJourney: 'User Journey',
            gantt: 'Gantt Chart',
            pie: 'Pie Chart',
            quadrant: 'Quadrant Chart',
            requirement: 'Requirement Diagram',
            gitgraph: 'Git Graph',
            c4: 'C4 Model',
          },
        },
        aiChangeActionBar: {
          accept: 'Accept',
          reject: 'Reject',
          acceptAll: 'Accept All',
          rejectAll: 'Reject All',
          aiSuggestion: 'AI Suggestion',
          processing: 'Processing...',
          acceptTooltip: 'Accept all AI suggested changes',
          rejectTooltip: 'Reject all AI suggested changes',
          viewDetails: 'View Details',
          changeDetails: 'Change Details',
          insertion: 'Insertion',
          deletion: 'Deletion',
        },
        tipTap: {
          saveFailedRetry: 'Save failed, click to retry',
          reSave: 'Resave',
          save: 'Save',
          saving: 'Saving...',
          pendingSave: 'Pending Save...',
          hideCatalog: 'Hide Catalog',
          showCatalog: 'Show Catalog',
          enterDocumentTitle: 'Please enter document title',
          startTypingContent: 'Start typing content...',
        },
        powerEdge: {
          title: 'Edge Tools',
          quickActions: 'Quick Actions',
          aiAssistant: 'AI Assistant',
          format: 'Format',
          analyze: 'Analyze',
          summarize: 'Summarize',
          suggest: 'Suggest',
        },
      },
      ConversitonContainer: {
        label: {
          new_conversation: 'New Conversation',
        },
        menu: {
          reset_font_size: 'Reset Font Size',
          increase_font_size: 'Increase Font Size',
          decrease_font_size: 'Decrease Font Size',
        },
        knowledge: {
          relevance: 'Relevance',
          knowledge_search_results: 'Knowledge Base Search Results',
          knowledge_search_results_count:
            'Found {count} related results in knowledge base "{knowledgeBaseName}"',
        },
        ai_question: {
          title: 'AI is asking you a question',
          input_placeholder: 'Please enter your answer',
          submit_answer: 'Submit Answer',
          cancel_question: 'Cancel Question',
        },
        input: {
          placeholder_conversation: 'Enter message...',
          placeholder_document: 'Enter message to start document conversation...',
          placeholder_new: 'Enter message to start a new conversation...',
        },
        tooltip: {
          selected_knowledge_base: 'Knowledge Base Selected',
          no_knowledge_base: 'No Knowledge Base Selected',
          selected_text_preview: 'Selected Text Preview',
          document_id: 'Document ID',
          position: 'Position',
          add_attachment: 'Add Attachment',
          upload_file: 'Upload File',
          upload_image: 'Upload Image',
          paste_clipboard: 'Paste Clipboard',
          upload_file_hint: 'Supports images, documents, and other files',
          upload_image_hint: 'Supports JPG, PNG, GIF formats',
          paste_clipboard_hint: 'Paste text or image',
          select_model: 'Select LLM',
          select_prompt: 'Select the LLM prompt to use',
          abort_conversation: 'Abort Conversation',
          aborting: 'Aborting...',
          send_message: 'Send Message',
          send_message_and_create_new_conversation: 'Send Message and Create New Conversation',
          prompt: 'Prompt',
          text_snippet: 'Snippet',
          document: 'Document',
          image: 'Image',
          file: 'File',
        },
        button: {
          llm_settings: 'LLM Settings',
        },
        warning: {
          no_tool_support:
            'The current model does not support tool calls, only conversation is available',
        },
        notification: {
          answer_cancelled: 'Answer Cancelled',
          cancel_failed: 'Cancel Failed',
          please_input_answer: 'Please enter your answer',
          answer_submitted: 'Answer Submitted',
          submit_failed: 'Submission Failed, Please Try Again',
          conversation_aborted: 'You have aborted the current reply',
          abort_failed: 'Abort Failed',
          file_added: 'File "{fileName}" has been added to attachments',
          image_added: 'Image "{fileName}" has been added to attachments',
          clipboard_image_added: 'Clipboard image has been added to attachments',
          clipboard_text_added: 'Clipboard text has been added to attachments',
          clipboard_empty: 'No available content in clipboard',
          clipboard_read_failed: 'Failed to read clipboard, please check browser permissions',
        },
      },
      settings: {
        // Settings panel
        SettingPannel: {
          base: 'App Settings',
          llm: 'LLM Provider',
          editor: 'Editor',
          knowledgeBase: 'Knowledge Base',
          searchEngine: 'Search Engine',
          resourceProvider: 'Resource Provider',
          autoComplete: 'Auto Complete',
          floatAgent: 'Float Agent',
          prompt: 'Prompt',
        },

        // LLM Settings
        LlmSettings: {
          title: 'LLM Settings',
          description: 'Configure and manage your LLM providers',
          providers: 'LLM Providers',
          currentProvider: 'Current Provider',
          enabled: 'Enabled',
          disabled: 'Disabled',
          noProviderSelected: 'Please select a provider to configure',
          enableRequired: 'You should enable at least one LLM provider',
        },

        // Knowledge Base Settings
        KnowledgeBaseSettings: {
          title: 'Knowledge Base Settings',
          description: 'Configure embedding models and vectorization parameters',
          embeddingMode: 'Embedding Mode',
          cloudApi: 'Cloud API',
          localModel: 'Local Model',
          autoSelect: 'Auto Select',
          selectProvider: 'Select Provider',
          selectEmbeddingModel: 'Select Embedding Model',
          embeddingDimension: 'Vector Dimension',
          embeddingDimensionHint:
            'Auto-detected embedding model vector dimension for HNSW index configuration',
          refreshDimension: 'Refresh Dimension',
          baseUrl: 'API URL',
          apiKey: 'API Key',
          model: 'Model',
          testConnection: 'Test Connection',
          connectionSuccess: 'Connection Successful',
          connectionFailed: 'Connection Failed',
          localWarning:
            'To ensure the accuracy of the knowledge base and local application efficiency, please select a sufficiently good local embedding model. If you are not using an NVIDIA graphics card or have less than 16G VRAM, please use the cloud API for vectorization, otherwise your experience may be affected!',

          // Local GGUF Config
          localGGUFConfig: 'Local GGUF Model Config',
          modelFilePath: 'Model File Path',
          modelFilePathHint: 'Select a local GGUF format embedding model file',
          gpuLayers: 'GPU Layers',
          gpuLayersHint: 'Number of layers accelerated by GPU, 0 for CPU only',
          contextSize: 'Context Size',
          contextSizeHint: 'Context window size of the model',
          enableGpuAcceleration: 'Enable GPU Acceleration',
          enableGpuAccelerationHint:
            'Enabling GPU acceleration can significantly improve inference speed, CUDA support required',

          // Model Status
          localModelLoaded: 'Local Model Loaded',
          localModelNotLoaded: 'Local Model Not Loaded',
          loadingModel: 'Loading Model...',
          unloadingModel: 'Unloading Model...',
          detectGpuCapabilities: 'Detect GPU Capabilities',
          loadModel: 'Load Model',
          unloadModel: 'Unload Model',
          testLocalModel: 'Test Local Model',

          // GPU Device
          gpuDeviceSelection: 'GPU Device Selection',
          gpuDeviceInfo: 'GPU Device Info',
          cpuMode: 'CPU Mode (No GPU)',
          maxLayers: 'Max Layers',
          recommendedLayers: 'Recommended Layers',
          memorySize: 'Memory Size',
          availableMemory: 'Available Memory',

          // Vectorization Params
          vectorizationParams: 'Vectorization Parameter Config',
          chunkSize: 'Document Chunk Size',
          chunkSizeHint: 'Max characters per document chunk',
          chunkOverlap: 'Document Chunk Overlap',
          chunkOverlapHint: 'Overlapping characters between adjacent chunks',
          semanticThreshold: 'Semantic Similarity Threshold',
          semanticThresholdHint: 'Semantic similarity threshold for chunking (0-1)',
          searchLimit: 'Search Result Limit',
          searchLimitHint: 'Max number of results returned in knowledge base search',

          // Chunking Strategy
          chunkStrategy: 'Default Chunking Strategy',
          chunkStrategyHint:
            'Default document chunking strategy when creating a new knowledge base',
          chunkStrategyDescription:
            'Select the default document chunking strategy when creating a new knowledge base. Different strategies are suitable for different types of document content.',

          // Test
          testApiConnection: 'Test API Connection',
          testVectorization: 'Test Vectorization',
          debugTool: 'Debug Tool',
          testGpuWorkflow: 'Test GPU Workflow',

          // Error Messages
          selectValidGGUF: 'Please select a valid GGUF model file',
          gpuLayersNegative: 'GPU layers cannot be negative',
          contextSizePositive: 'Context size must be greater than 0',
          chunkSizePositive: 'Document chunk size must be greater than 0',
          chunkOverlapNonNegative: 'Document chunk overlap cannot be negative',
          searchLimitPositive: 'Search result limit must be greater than 0',

          // Auth Errors
          authFailed: 'Authentication failed: Please check if the API Key is correct',
          serviceNotFound: 'Service not found: Please check if the Base URL is correct',
          networkError: 'Network error: Please check your network connection and server status',
          requestTimeout: 'Request timeout: Server response took too long',
          modelError: 'Model error: Please check if the model name is correct',
          quotaExceeded: 'Quota exceeded: Please check your account balance and quota limits',

          // Test Messages
          connectionTestSuccess:
            'Connection test successful! Model: {model}, Dimension: {dimension}, Usage: {tokens} tokens',
          vectorizationTestSuccess:
            'Vectorization test successful! Model: {model}, Dimension: {dimension}, Similarity: {similarity}, Usage: {tokens} tokens',
          localModelTestSuccess:
            'Local GGUF model test successful! Dimension: {dimension}, Inference time: {time}ms, GPU acceleration: {gpu}',

          // Status Messages
          gpuDetectionSuccess: 'GPU detection successful! Found {count} GPU devices',
          gpuNotAvailable: 'GPU not available, will use CPU for inference',
          gpuDetectionFailed: 'GPU detection failed: {error}',

          selectedProvider: 'Selected: ',
          test_gpu_start: 'Starting GPU test...',
          test_gpu_success: 'GPU test successful! Current device: {device}',
          test_gpu_failed: 'GPU test failed: {error}',
        },

        // Editor Settings
        EditorSettings: {
          title: 'Editor Settings',
          fontSize: 'Font Size',
          lineHeight: 'Line Height',
          theme: 'Theme',
          light: 'Light',
          dark: 'Dark',
          autoSave: 'Auto Save',
          autoComplete: 'Auto Complete',
          floatAgent: 'Float Agent',
          enableToolbar: 'Show Toolbar',
          enableEmbeddedTitle: 'Show Embedded Title',
        },

        // Base Settings
        BaseSettings: {
          title: 'Base Settings',
          interfaceLanguage: 'Interface Language',
          languageDescription:
            'Change the display language of the interface, settings will be saved automatically',
          interfaceTheme: 'Interface Theme',
          themeDescription:
            'Change the display theme of the interface, settings will be saved automatically',
          interfaceThemeSaveFailed: 'Interface theme settings failed',
        },

        // Auto Complete Settings
        AutoCompleteSetting: {
          title: 'Auto Complete Settings',
          enableAutoComplete: 'Enable Auto Complete',
          triggerMode: 'Trigger Mode',
          manual: 'Manual Trigger',
          automatic: 'Automatic Trigger',
          delay: 'Trigger Delay',
          seconds: 'Seconds',
          selectProvider: 'Select Provider',
          selectModel: 'Select Auto Complete Model',
          globalPrompt: 'Global Prompt',
          maxTokens: 'Max Token Count',
          temperature: 'Temperature',
          frequencyPenalty: 'Frequency Penalty',
          presencePenalty: 'Presence Penalty',
          providerHint: 'Select an LLM provider with text generation/inference model',
          modelHint: 'Select a model for auto complete',
          promptHint:
            'Global prompt for auto complete. When editing documents, necessary prompts will be provided automatically. No need to set unless required.',
          maxTokensHint: 'Maximum number of tokens returned by auto complete',
          temperatureHint:
            'The higher the value, the more creative the model; the lower, the more rigorous',
          frequencyPenaltyHint:
            'Penalizes new tokens based on their frequency in the text to reduce repetition',
          presencePenaltyHint:
            'Penalizes new tokens if they have already appeared, increasing the chance of new topics',
          needLlmProvider: 'At least one LLM provider must be enabled to use auto complete',
          enableHint: 'Enable smart code completion in the editor',
          selectedInfo: 'Selected: {provider} / {model}',
          apiConfigHint: 'API configuration will be automatically obtained from LLM settings',
          advancedSettings: 'Advanced Settings',
          triggerLength: 'Trigger Length',
          triggerLengthHint: 'Minimum number of characters to trigger auto complete',
          maxSuggestions: 'Max Suggestions',
          maxSuggestionsHint: 'Maximum number of suggestions to display simultaneously',
          debounceTime: 'Debounce Time',
          debounceTimeHint: 'How long to wait after input stops before triggering auto complete',
          milliseconds: 'milliseconds',
        },
        FloatAgentSetting: {
          title: 'Float Agent Settings',
          enableFloatAgent: 'Enable Float Agent',
          triggerMode: 'Trigger Mode',
          manual: 'Manual Trigger',
          automatic: 'Automatic Trigger',
          delay: 'Trigger Delay',
          seconds: 'Seconds',
          selectProvider: 'Select Provider',
          selectModel: 'Select Float Agent Model',
          globalPrompt: 'Global Prompt',
          maxTokens: 'Max Token Count',
          temperature: 'Temperature',
          frequencyPenalty: 'Frequency Penalty',
          presencePenalty: 'Presence Penalty',
          providerHint: 'Select an LLM provider with text generation/inference model',
          modelHint: 'Select a model for float agent',
          promptHint:
            'Global prompt for float agent. When editing documents, necessary prompts will be provided automatically. No need to set unless required.',
          maxTokensHint: 'Maximum number of tokens returned by float agent',
          temperatureHint:
            'The higher the value, the more creative the model; the lower, the more rigorous',
          frequencyPenaltyHint:
            'Penalizes new tokens based on their frequency in the text to reduce repetition',
          presencePenaltyHint:
            'Penalizes new tokens if they have already appeared, increasing the chance of new topics',
          needLlmProvider: 'At least one LLM provider must be enabled to use float agent',
          enableHint: 'Enable float agent in the editor',
          selectedInfo: 'Selected: {provider} / {model}',
          apiConfigHint: 'API configuration will be automatically obtained from LLM settings',
          advancedSettings: 'Advanced Settings',
          triggerLength: 'Trigger Length',
          triggerLengthHint: 'Minimum number of characters to trigger float agent',
        },

        PromptSettings: {
          promptName: 'New Prompt Name',
          add: 'Add',
          delete: 'Delete',
          responsibilities: 'Responsibilities',
          emphasize: 'Emphasize',
          autoCompleteDescription: 'Automatically complete based on cursor position and context',
          floatAgentDescription: 'Rewrite or continue writing selected text or at cursor position',
          chatDescription: 'Execute operations based on user requirements in conversation',
          newResponsibility: 'New Responsibility',
          autoComplete: 'Auto Complete',
          floatAgent: 'Float Agent',
          chat: 'Chat',
        },

        // Search Engine Settings
        SearchEngineSettings: {
          title: 'Search Engine Settings',
          description: 'Configure search engine parameters',
          provider: 'Search Engine',
          apiKey: 'API Key',
          baseUrl: 'API URL',
          selectProvider: 'Select Search Engine Provider',
          selectHint: 'Select the provider to configure from the list on the left',
        },

        // Resource Provider Settings
        ResourceProviderSettings: {
          title: 'Resource Provider Settings',
          description: 'Configure resource provider parameters',
          provider: 'Resource Provider',
          apiKey: 'API Key',
          baseUrl: 'API URL',
          selectProvider: 'Select Resource Provider',
          selectHint: 'Select the provider to configure from the list on the left',
        },

        // Model Manager
        ModelManager: {
          title: 'Model Management',
          description: 'Manage local and cloud models',
          localModels: 'Local Models',
          cloudModels: 'Cloud Models',
          refresh: 'Refresh',
          addModel: 'Add Model',
          removeModel: 'Remove Model',
          modelName: 'Model Name',
          modelSize: 'Model Size',
          modelPath: 'Model Path',
          noModels: 'No models yet, click "Add Model" in the upper right to start configuration',
          selectModel: 'Select',
          inputModel: 'Input',
          selectRequired: 'Please select a model',
          inputRequired: 'Please enter a model name',
          modelExists: 'Model already exists',
          toolsCalls: 'Tool Calls',
          newCategory: 'New Category',
          removeModelTooltip: 'Click × to delete model',
        },
        // LLM Provider Settings
        llmProviders: {
          baseUrl: 'API Base URL',
          baseUrlRules: {
            required: 'Please enter the API Base URL',
            hint: 'DeepSeek API service address',
          },
          apiKeyRules: {
            required: 'Please enter the API Key',
            hint: 'API service address',
          },
          apiKey: 'API Key',
          maxTokens: 'Max Output Tokens',
          temperature: 'Temperature',
          topP: 'Top P',
          frequencyPenalty: 'Frequency Penalty',
          presencePenalty: 'Presence Penalty',
          seed: 'Random Seed',
          parallelToolCalls: 'Enable Parallel Tool Calls',
          enable: 'Enable',
          advancedSettings: 'Advanced Settings',
          modelManagement: 'Available Model Management',
          temperatureDescription:
            'Controls the randomness of model output. Higher values make output more random, lower values make it more focused and deterministic.',
          topPDescription:
            'Probability threshold for nucleus sampling. Higher values increase randomness, lower values increase determinism.',
          frequencyPenaltyDescription:
            'Positive values penalize new tokens based on their existing frequency in the text, reducing repetition.',
          presencePenaltyDescription:
            'Positive values penalize new tokens if they have already appeared, increasing the chance of new topics.',
          seedDescription: 'Set a random seed for more deterministic output (optional)',
          parallelToolCallsDescription:
            'Allow the model to call multiple tools in a single response for higher efficiency',
          apiKeyHint: 'Please enter the API Key',
          baseUrlHint: 'Please enter the API Base URL',
          maxTokensHint: 'Maximum number of tokens output by the model',
          temperatureHint: 'Controls the randomness of model output',
          topPHint: 'Probability threshold for nucleus sampling',
          frequencyPenaltyHint: 'Penalize repeated content based on existing frequency',
          presencePenaltyHint: 'Penalize based on whether new tokens have appeared',
          seedHint: 'Set a random seed for deterministic output',
          titleSuffix: ' connection parameters and model options',
          enabled: 'Enabled',

          serviceEndpoint: 'Service Endpoint',
          apiKeyRueles: {
            required: 'Please enter the API Key',
            hint: 'API service address',
          },
          getApiKey: 'Get API Key',
          maxTokensRules: {
            required: 'Please enter a valid token count',
            hint: 'Maximum number of tokens output by the model (default 4096)',
          },
          randomnessType: 'Randomness Control Type',
          thinkTooltip:
            'Allow Claude to perform deeper internal reasoning for better answers to complex questions',
          thinkInputLabel: 'Thinking Budget (Tokens)',
          thinkInputRules: {
            required: 'Thinking budget must be at least 1024 tokens',
            hint: 'Maximum number of tokens for internal reasoning',
          },
          enableInterleavedThinking: 'Enable Interleaved Thinking (Beta)',
          enableInterleavedThinkingTooltip:
            'Allow Claude to think between tool calls for more accurate tool usage',
          authType: 'Authentication Type',
          authTypeRules: {
            required: 'Please select authentication type',
            hint: 'Select authentication type (currently only API Key supported)',
          },
          parallelToolCallsTooltip:
            'Allow the model to call multiple tools in a single response for higher efficiency',
          seedRules: {
            required: 'Please enter a random seed',
            hint: 'Set a random seed for more deterministic output (optional)',
          },
          topK: 'Top K',
          topKDescription:
            'Number of tokens considered during sampling. Smaller values make output more focused, larger values increase diversity.',
          candidateCount: 'Candidate Count',
          candidateCountRules: {
            required: 'Please enter a number between 1 and 8',
            hint: 'Number of candidate responses generated (1-8)',
          },
          stream: 'Streaming Output',
          streamTooltip: 'Enable streaming output to see generated content in real time',
          stopSequences: 'Stop Sequences',
          stopSequencesRules: {
            hint: 'Enter stop sequences, separated by commas (optional)',
          },
          testConnection: 'Test Connection',
          temperatureOptions: {
            label: 'Temperature',
            value: 'temperature',
          },
          topPOptions: {
            label: 'Top P',
            value: 'topP',
          },
          enableParallelToolCalls: 'Enable Parallel Tool Calls',
          frequencyPenaltyValue: 'Frequency Penalty Value',
          presencePenaltyValue: 'Presence Penalty Value',
          seedValue: 'Random Seed Value',
          temperatureValue: 'Temperature Value',
          topPValue: 'Top P Value',

          // Provider specific settings
          qwen: {
            title: 'Qwen Settings',
            description: 'Configure Qwen service connection parameters and model options',
            baseUrlHint: 'Qwen LLM API service address',
            apiKeyGet: 'Get Qwen API Key',
          },

          openai: {
            title: 'OpenAI Settings',
            description: 'Configure OpenAI API connection parameters and model options',
            baseUrlHint: 'OpenAI API service address',
            apiKeyGet: 'Get OpenAI API Key',
          },

          anthropic: {
            title: 'Anthropic Settings',
            description:
              'Configure Anthropic Claude service connection parameters and model options',
            baseUrlHint: 'Anthropic API service address',
            apiKeyGet: 'Get Anthropic API Key',
            thinkingEnabled: 'Enable Chain-of-Thought',
            thinkingEnabledDescription:
              'Enable chain-of-thought to let the model show its reasoning process',
          },

          gemini: {
            title: 'Gemini Settings',
            description: 'Configure Google Gemini service connection parameters and model options',
            baseUrlHint: 'Google AI API service address',
            apiKeyGet: 'Get Google AI API Key',
          },

          moonshot: {
            title: 'Moonshot Settings',
            description: 'Configure Moonshot AI service connection parameters and model options',
            baseUrlHint: 'Moonshot AI API service address',
            apiKeyGet: 'Get Moonshot AI API Key',
          },

          deepseek: {
            title: 'DeepSeek Settings',
            description: 'Configure DeepSeek service connection parameters and model options',
            baseUrlHint: 'DeepSeek API service address',
            apiKeyGet: 'Get DeepSeek API Key',
          },

          minimax: {
            title: 'MiniMax Settings',
            description: 'Configure MiniMax service connection parameters and model options',
            baseUrlHint: 'MiniMax API service address',
            apiKeyGet: 'Get MiniMax API Key',
          },

          ollama: {
            title: 'Ollama Settings',
            description: 'Configure local Ollama service connection parameters and model options',
            baseUrlHint: 'Ollama local service address',
            apiKeyGet: 'Ollama does not require an API Key',
          },

          azureOpenai: {
            title: 'Azure OpenAI Settings',
            description: 'Configure Azure OpenAI service connection parameters and model options',
            baseUrlHint: 'Azure OpenAI service address',
            apiKeyGet: 'Get Azure OpenAI API Key',
            deploymentName: 'Deployment Name',
            deploymentNameRules: {
              required: 'Please enter the deployment name',
              hint: 'Model deployment name created in Azure Portal',
            },
            apiVersion: 'API Version',
            apiVersionRules: {
              required: 'Please select the API version',
              hint: 'Select the API version to use',
            },
          },

          volces: {
            title: 'Volcengine Settings',
            description: 'Configure Volcengine service connection parameters and model options',
            baseUrlHint: 'Volcengine API service address',
            apiKeyGet: 'Get Volcengine API Key',
          },

          grok: {
            title: 'Grok Settings',
            description: 'Configure xAI Grok service connection parameters and model options',
            baseUrlHint: 'xAI API service address',
            apiKeyGet: 'Get xAI API Key',
          },

          glm: {
            title: 'GLM Settings',
            description: 'Configure GLM service connection parameters and model options',
            baseUrlHint: 'GLM API service address',
            apiKeyGet: 'Get GLM API Key',
          },
        },

        // Resource Provider Settings
        resourceProviders: {
          apiKey: 'API Key',
          baseUrl: 'Base URL',
          maxResults: 'Max Results',
          searchDepth: 'Search Depth',
          includeAnswer: 'Include AI Answer',
          includeRawContent: 'Include Raw Content',
          includeImages: 'Include Images',
          includeImageDescriptions: 'Include Image Descriptions',
          apiKeyPlaceholder: 'Please enter your API Key',
          apiKeyHint: 'Used to access API',
          baseUrlPlaceholder: 'Please enter the API base address',
          baseUrlHint: 'API base address',
          maxResultsPlaceholder: 'Please enter max results',
          maxResultsHint: 'Set the maximum number of results returned by search',
          searchOptions: 'Search Options',
          includeAnswerHint: 'Whether to include AI-generated answer summary in search results',
          includeRawContentHint: 'Whether to include raw web content in search results',
          includeImagesHint: 'Whether to include related images in search results',
          includeImageDescriptionsHint:
            'Whether to generate descriptions for images (requires images enabled first)',

          // Provider specific
          pexels: {
            title: 'Pexels Settings',
            description: 'Configure Pexels image service',
          },

          tavily: {
            title: 'Tavily Search Settings',
            description: 'Configure Tavily search engine',
            searchDepthOptions: {
              basic: 'Basic Search',
              advanced: 'Advanced Search',
            },
          },
        },

        knowledgeSearch: 'Knowledge Base',
        pexelsIntegration: 'Media Library',

        // Search & Replace
        searchReplace: {
          title: 'Find & Replace',
          search: 'Find',
          replace: 'Replace',
          replaceAll: 'Replace All',
          caseSensitive: 'Case Sensitive',
          wholeWord: 'Whole Word',
          useRegex: 'Use Regex',
          noResults: 'No matches found',
          results: '{count} matches found',
          confirmReplaceAll: 'Confirm replace all matches?',
          replaceSuccess: '{count} matches replaced successfully',
          searchPlaceholder: 'Find...',
          replacePlaceholder: 'Replace with...',
          clearSearch: 'Clear Search',
          next: 'Next',
          previous: 'Previous',
        },

        // Common actions
        common: {
          save: 'Save',
          reset: 'Reset',
          cancel: 'Cancel',
          confirm: 'Confirm',
          delete: 'Delete',
          edit: 'Edit',
          add: 'Add',
          test: 'Test',
          enable: 'Enable',
          disable: 'Disable',
          enabled: 'Enabled',
          disabled: 'Disabled',
          times: 'times',
          settings: 'Settings',
        },

        providers: {
          PexelsProviderConfig: {
            apiKeyLabel: 'Pexels API Key',
            apiKeyPlaceholder: 'Please enter Pexels API Key',
            apiKeyHint: 'Pexels API Key',
            apiKeyRules: {
              required: 'Please enter Pexels API Key',
            },
            baseUrlLabel: 'Base URL',
            baseUrlPlaceholder: 'https://api.pexels.com',
            baseUrlHint: 'Pexels API base address',
            maxResultsLabel: 'Max Results',
            maxResultsPlaceholder: 'Please enter max results',
            maxResultsHint: 'Set the maximum number of images returned by search',
          },
          TavilyProviderConfig: {
            apiKeyLabel: 'Tavily API Key',
            apiKeyPlaceholder: 'Please enter Tavily API Key',
            apiKeyHint: 'Tavily API Key',
            apiKeyRules: {
              required: 'Please enter Tavily API Key',
            },
            baseUrlLabel: 'Base URL',
            baseUrlPlaceholder: 'https://api.tavily.com/search',
            baseUrlHint: 'Tavily API base address',
            searchDepthLabel: 'Search Depth',
            searchOptions: 'Search Options',
            searchDepthOptions: {
              basic: 'Basic Search',
              advanced: 'Advanced Search',
            },
            searchDepthHint: 'Select the depth level of search',
            maxResultsLabel: 'Max Results',
            maxResultsPlaceholder: 'Please enter max results',
            maxResultsHint: 'Set the maximum number of results returned by search',
            includeAnswerLabel: 'Include AI Answer',
            includeAnswerHint: 'Whether to include AI-generated answer summary in search results',
            includeRawContentLabel: 'Include Raw Content',
            includeImagesLabel: 'Include Images',
            includeImageDescriptionsLabel: 'Include Image Descriptions',
            includeRawContentHint: 'Whether to include raw web content in search results',
            includeImagesHint: 'Whether to include related images in search results',
            includeImageDescriptionsHint:
              'Whether to generate descriptions for images (requires images enabled first)',
          },
        },
      },
      pages: {
        KnowledgeBasePage: {
          notConfigured: {
            title: 'Knowledge Base Not Configured',
            description:
              'Please configure knowledge base parameters (API URL, key, and model) in settings and pass the connection test before using the knowledge base feature.',
            buttonLabel: 'Go to Configure',
          },
        },
      },
      AddDocumentDialog: {
        title: 'Add Document from Library',
        searchPlaceholder: 'Search document title...',
        selectedCount: '{count} selected',
        loading: 'Loading document list...',
        noMatch: 'No matching document found',
        noDocuments: 'No documents available in the library to add',
        unnamedDocument: 'Untitled Document',
        addSelectedDocuments: 'Add Selected Documents',
        tryOtherKeywords: 'Please try other search keywords',
        allDocumentsAdded: 'All documents may have been added to the current knowledge base',
        totalDocuments: 'Total {count} documents',
      },
      AssistantMessage: {
        thinking: 'Thinking...',
        thinkingProcess: 'Thinking Process',
        toolCall: 'AI Tool Call',
        saveToKnowledge: 'Save to Knowledge Base',
        saveToKnowledgeHint: 'Please configure knowledge base parameters in settings first',
        aiAnswer: 'AI Answer',
      },
      ChatConversationList: {
        history: 'Conversation History',
        showLess: 'Show Less',
        showAll: 'Show All',
        rename: 'Rename',
        delete: 'Delete',
        renameDialog: 'Rename Conversation',
        conversationTitle: 'Conversation Title',
        cancel: 'Cancel',
        confirm: 'Confirm',
        loadFailed: 'Failed to load conversation list',
        createFailed: 'Failed to create conversation',
        renameSuccess: 'Conversation renamed successfully',
        renameFailed: 'Rename failed',
        confirmDelete: 'Confirm Delete',
        confirmDeleteMessage:
          'Are you sure you want to delete conversation "{title}"? This action cannot be undone.',
        cancelDelete: 'User Cancelled',
        deleteSuccess: 'Conversation deleted successfully',
        deleteFailed: 'Failed to delete conversation',
      },
      ChunkingStrategySelector: {
        title: 'Chunking Strategy Configuration',
        description:
          'Select an appropriate chunking strategy to process document content. Different strategies are suitable for different types of documents.',
        methodLabel: 'Chunking Method',
        methodOptions: {
          markdown: 'Markdown',
          recursiveCharacter: 'Recursive Character',
        },
        comingSoon: 'Coming Soon',
        configuration: 'Configuration Parameters',
        chunkSize: 'Chunk Size',
        chunkOverlap: 'Overlap Size',
        characters: 'Characters',
        chunkSizeRule1: 'Must be greater than 0',
        chunkSizeRule2: 'Cannot exceed 5000 characters',
        chunkOverlapRule1: 'Cannot be less than 0',
        chunkOverlapRule2: 'Overlap cannot exceed chunk size',
        markdownSuggestion:
          'Suggestion: Use 600-1000 characters for Markdown documents to maintain heading structure',
        latexSuggestion:
          'Suggestion: Use 800-1200 characters for LaTeX documents to maintain formula and environment integrity',
        recursiveCharacterSuggestion:
          'Suggestion: Use 500-800 characters for plain text, split at natural boundaries',
        smartSuggestion:
          'Suggestion: Smart chunking will automatically select the best strategy, using 600-1000 characters',
        defaultSuggestion:
          'Suggestion: Use 500-800 characters for general documents, 800-1200 for technical documents',
        strategyInfo: 'Strategy Details',
        useCases: 'Use Cases',
        mainAdvantages: 'Main Advantages',
        usageRestrictions: 'Usage Restrictions',
        loadFailed: 'Failed to load chunking methods',
        markdown: 'Markdown',
        markdownDescription: 'Structured chunking for Markdown documents',
        configFailed: 'Configuration validation failed',
        configValid: 'Configuration valid',
        configInvalid: 'Invalid configuration parameters: {errors}',
        recursiveCharacter: 'Recursive Character',
        recursiveCharacterDescription: 'Recursive character chunker',
      },
      ConversitionHistory: {
        addConversation: 'New Conversation',
        deleteConversation: 'Delete Conversation',
      },
      CreateDocument: {
        title: 'Document Name',
      },
      CreateDocumentDialog: {
        title: 'Create Document',
        rule: 'Please enter document title',
        processing: 'Processing...',
        save: 'Save',
        cancel: 'Cancel',
        document_config: 'Document Configuration',
        chunking_progress: 'Chunking Progress',
        chunking_result: 'Chunking Result',
        chunking_count: 'Chunk Count',
        average_chunk_size: 'Average Chunk Size',
        chunking_size_range: 'Size Range',
        characters: 'Characters',
        chunking_completed:
          'Document chunking completed! {count} chunks, average size {averageSize} characters',
        chunking_failed: 'Document chunking failed: {error}',
        document_creating:
          'Document is being created, chunking task has been submitted for background processing...',
        submit_chunking_failed: 'Failed to submit chunking task: {error}',
        unsaved_changes: 'You have unsaved changes, are you sure you want to close?',
        confirm_close: 'Confirm Close',
        please_input_title: 'Please enter document title',
        please_input_content: 'Please enter document content',
        please_check_chunking_config: 'Please check chunking strategy configuration',
      },
      CreateFolder: {
        title: 'Folder Name',
      },
      CreatekKnowlegeDocCard: {
        save_to_knowledge_base: 'Save to Knowledge Base',
        select_knowledge_base_and_set_title: 'Select knowledge base and set document title',
        document_title: 'Document Title',
        select_knowledge_base: 'Select Knowledge Base',
        no_knowledge_base: 'No knowledge base available, please create one first',
        create_new_knowledge_base: 'Create New Knowledge Base',
        content_preview: 'Content Preview',
        save: 'Save',
        please_input_title: 'Please enter document title',
        please_input_knowledge_base_name: 'Please enter knowledge base name',
        knowledge_base_name: 'Knowledge Base Name',
        knowledge_base_description: 'Knowledge Base Description',
        create: 'Create',
        cancel: 'Cancel',
        already_added: 'Already added to current knowledge base',
      },
      DocManager: {
        newFolder: 'New Folder',
        customSort: 'Custom Sort',
        alphabeticalSort: 'Sort by Name',
      },
      DragDropIndicator: {
        dropHere: 'Drop Here',
      },
      DocumentItem: {
        add_to_conversation: 'Add to Conversation',
        add_to_knowledge_base: 'Add to Knowledge Base',
        quick_add_to_knowledge_base: 'Add to Knowledge Base menu',
        rename: 'Rename',
        copy: 'Copy',
        cut: 'Cut',
        delete: 'Delete',
        document_not_in_knowledge_base: 'Document not added to knowledge base',
        knowledge_document_not_found: 'Corresponding knowledge document not found',
        already_added: 'Alreay added',
        document_already_in_knowledge_base: 'Already added to "{name}",cannot add again',
      },
      FolderItem: {
        new_subfolder: 'New Subfolder',
        new_document: 'New Document',
        rename: 'Rename',
        paste: 'Paste',
        add_all_to_knowledge_base: 'Add All to Knowledge Base',
        delete: 'Delete',
        creating_content_cannot_fold: 'Cannot collapse while creating content',
        no_document_to_paste: 'No document to paste',
        document_moved_to_folder: 'Document "{title}" has been moved to folder',
        document_copied_to_folder: 'Document "{title}" has been copied to folder',
        paste_document_failed: 'Failed to paste document',
        confirm_delete_folder_title: 'Confirm Delete Folder',
        confirm_delete_folder_message:
          'Are you sure you want to delete folder "{name}"? This action cannot be undone.',
        folder_deleted_success: 'Folder "{name}" has been successfully deleted',
        folder_deleted_failed: 'Failed to delete folder "{name}": {error}',
      },
      BatchKnowledgeBaseConfigCard: {
        batch_add_to_knowledge_base: 'Batch Add to Knowledge Base',
        select_knowledge_base_and_chunking_strategy: 'Select Knowledge Base and Chunking Strategy',
        select_knowledge_base: 'Select Knowledge Base',
        please_select_knowledge_base: 'Please Select Knowledge Base',
        no_knowledge_base: 'No Knowledge Base Available',
        create_new_knowledge_base: 'Create New Knowledge Base',
        chunking_strategy: 'Chunking Strategy',
        chunking_method: 'Chunking Method',
        recursive_character_text_splitter: 'Recursive Character Text Splitter',
        markdown_text_splitter: 'Markdown Text Splitter',
        latex_text_splitter: 'LaTeX Text Splitter',
        smart_text_splitter: 'Smart Text Splitter',
        chunk_size: 'Chunk Size',
        chunk_overlap: 'Chunk Overlap',
        cancel: 'Cancel',
        start_batch_processing: 'Start Batch Processing',
      },
      KnowledgeBaseDetail: {
        upload_document: 'Upload Document',
        create_document: 'Create Document',
        add_document_from_document_library: 'Add Document from Library',
        loading: 'Loading...',
        no_description: 'No Description',
        created_time: 'Created Time',
        documents: 'Documents',
        knowledge_chunks: 'Chunks',
        knowledge_base_documents: 'Knowledge Base Documents',
        no_documents: 'No Documents',
        click_add_document: 'Click to Add Document',
        type: 'Type',
        editor_document: 'Edit Document',
        processing_vectorization: 'Processing Vectorization...',
        add_time: 'Added Time',
        chunk_count: 'Chunk Count',
        load_failed: 'Load Failed',
        cannot_load_knowledge_base_information: 'Cannot load knowledge base information',
        retry: 'Retry',
        upload_document_to_knowledge_base: 'Upload Document to Knowledge Base',
        select_document_file: 'Select Document File',
        document_title: 'Document Title',
        please_input_document_title: 'Please enter document title',
        document_description: 'Document Description',
        cancel: 'Cancel',
        upload: 'Upload',
        save: 'Save',
        document_configuration: 'Document Configuration',
        add_document_to_knowledge_base: 'Add Document to Knowledge Base',
        search_document: 'Search Document',
        update_time: 'Update Time',
        add_selected_documents: 'Add Selected Documents',
        knowledge_base_settings: 'Knowledge Base Settings',
        knowledge_base_name: 'Knowledge Base Name',
        knowledge_base_description: 'Knowledge Base Description',
        settings: 'Settings',
        close_editor: 'Close Editor',
        document_not_found: 'Document not found',
        document_id: 'Document ID',
        document_may_be_deleted_or_data_synchronization_exception:
          'Document may have been deleted or data synchronization exception',
        document_type: 'Document Type',
        unknown: 'Unknown',
        not_support_online_editing: 'Online editing not supported',
        only_support_markdown_and_text_type_document_editing:
          'Only Markdown and text type document editing is supported',
        open_document_failed: 'Failed to open document',
        unknown_error: 'Unknown error',
        confirm_close: 'Document saved successfully, vectors updated',
        document_has_been_modified_but_not_saved:
          'Document has been modified but not saved, are you sure you want to close?',
        please_input_document_content: 'Please enter document content',
        document_saved_successfully: 'Document saved successfully, vectors updated',
        save_document_failed: 'Failed to save document',
        document_content_is_empty: 'Content is empty',
        skip_adding: 'Skip Adding',
        unnamed_document: 'Untitled Document',
        add_document_failed: 'Failed to add document',
        duplicate_document: 'Duplicate document name exists',
        already_exists: 'Already exists',
        successfully_added: 'Successfully added',
        documents_to_knowledge_base: 'documents to knowledge base',
        skipped: 'Skipped',
        duplicate_documents: 'duplicate documents',
        documents_failed_to_add: 'documents failed to add',
        all_selected_documents_already_exist_in_the_knowledge_base:
          'All selected documents already exist in the knowledge base',
        confirm_remove: 'Confirm Remove',
        are_you_sure_you_want_to_remove_the_document: 'Are you sure you want to remove:',
        remove_document_failed: 'Failed to remove document',
        please_input_knowledge_base_name: 'Please enter knowledge base name',
        settings_format_is_incorrect: 'Settings format is incorrect, please enter valid JSON',
        settings_saved_successfully: 'Settings saved successfully',
        save_settings_failed: 'Failed to save settings',
        database_is_not_initialized_yet: 'Database is not initialized yet, please try again later',
        document_upload_successfully: 'Document uploaded successfully',
        upload_document_failed: 'Failed to upload document, please try again',
        knowledge_base_function_is_temporarily_unavailable:
          'Knowledge base function is temporarily unavailable, please restart the application',
        file_read_failed: 'Failed to read file',
        document_created_successfully: 'Document created successfully',
        create_document_failed: 'Failed to create document',
        document_vectorized_successfully: 'Document vectorized successfully',
        generated: 'Generated',
        knowledge_fragments: 'knowledge fragments',
        document_vectorization_failed: 'Document vectorization failed',
        data_refresh_completed: 'Data refresh completed',
        data_refresh_failed: 'Data refresh failed',
        database_is_not_initialized: 'Database is not initialized',
        using_default_chunking_strategy: 'Using default chunking strategy',
        fallback_create_failed: 'Fallback creation also failed',
        chunking_failed: 'Document chunking failed',
      },
      KnowledgeBaseDetailView: {
        upload_document: 'Upload Document',
        create_document: 'Create Document',
        add_document_from_document_library: 'Add Document from Library',
        add_document_from_document_library_tip:
          'Please right-click on File / Folder to add to knowledge base',
        refresh: 'Refresh',
        settings: 'Settings',
        loading: 'Loading...',
        documents: 'Documents',
        no_description: 'No Description',
        created_time: 'Created Time',
        no_documents: 'No Documents',
        click_add_document: 'Click to Add Document',
        type: 'Type',
        editor_document: 'Edit Document',
        processing_vectorization: 'Processing Vectorization...',
        add_time: 'Added Time',
        chunk_count: 'Chunk Count',
        knowledge_chunks: 'Chunks',
        knowledge_base_documents: 'Knowledge Base Documents',
        vectorization_completed: 'Vectorization Completed',
        delete_document: 'Delete Document',
        cannot_load_knowledge_base_information: 'Cannot load knowledge base information',
        retry: 'Retry',
        load_failed: 'Load Failed',
        revectorization: 'Revectorization',
        need_revectorization: 'Need revectorization',
        revectorization_knowledge_base: 'Revectorization Knowledge Base',
        revectorize_confirm:
          'Are you sure you want to revectorize the document "{title}"? This will delete all existing chunks and vectors, and re-chunk and vectorize using the current knowledge base configuration.',
        revectorize_started: 'Revectorizing document "{title}"...',
        chunking_completed: 'Document "{title}" chunking completed, generated {chunks} chunks',
        revectorize_error: 'Revectorization failed: {error}',
        remove: 'Remove',
        ignoreRevectorize: 'Ignore Revectorization',
        ignoreRevectorizeConfirm: {
          title: 'Ignore Revectorization',
          message:
            'Are you sure you want to ignore the revectorization requirement for document "{title}"? This will update the knowledge document timestamp to make the system consider it up-to-date.',
        },
        ignoreRevectorizeSuccess: 'Ignored revectorization requirement for document "{title}"',
        ignoreRevectorizeError: 'Failed to ignore revectorization: {error}',
      },
      KnowledgeBaseList: {
        rag_knowledge_base_management: 'RAG Knowledge Base Management',
        loading: 'Loading...',
        no_knowledge_base: 'No Knowledge Base',
        click_create_knowledge_base:
          'Click "Create Knowledge Base" to start creating your first knowledge base',
        document_count: 'Document Count',
        created_time: 'Created Time',
        edit: 'Edit',
        delete: 'Delete',
        back: 'Back',
      },
      KnowledgeBaseManager: {
        document_title: 'Document Title',
        please_input_document_title: 'Please enter document title',
        save: 'Save',
        edit_original_document: 'Edit Original Document',
        document_created_successfully: 'Document created successfully',
        close_editor: 'Close Editor',
        confirm_delete: 'Confirm Delete',
        confirm_delete_message:
          'Are you sure you want to delete knowledge base "{name}"? This action cannot be undone.',
        database_is_not_initialized_yet: 'Database is not initialized yet, please try again later',
        create_document_failed: 'Failed to create document',
        create_document_successfully: 'Document created successfully',
        document_upload_successfully: 'Document uploaded successfully',
        chunking_failed: 'Document chunking failed',
        unknown_error: 'Unknown error',
        successfully_added: 'Successfully added',
        documents_to_knowledge_base: 'documents to knowledge base',
        skipped: 'Skipped',
        duplicate_documents: 'duplicate documents',
        documents_failed_to_add: 'documents failed to add',
        all_selected_documents_already_exist_in_the_knowledge_base:
          'All selected documents already exist in the knowledge base',
        confirm_remove: 'Confirm Remove',
        chunking_result_submitted:
          'Chunking result submitted! {chunkCount} chunks, starting vectorization...',
        submit_chunking_result_failed: 'Failed to submit chunking result',
        unnamed_document: 'Untitled Document',
        document_not_found: 'Document not found',
        document_type: 'Document Type',
        unknown: 'Unknown',
        not_support_online_editing: 'Online editing not supported',
        open_document_failed: 'Failed to open document',
        confirm_close: 'Confirm Close',
        document_has_been_modified_but_not_saved:
          'Document has been modified but not saved, are you sure you want to close?',
        document_saved_successfully: 'Document saved successfully, vectors updated',
        save_document_failed: 'Failed to save document',
        add_document_failed: 'Failed to add',
        confirm_remove_message:
          'Are you sure you want to remove document "{name}" from the knowledge base?',
        document_vectorized_successfully: 'Document vectorized successfully',
        document_vectorization_failed: 'Document vectorization failed',
        document_id: 'Document ID',
        generated: 'Generated',
        knowledge_fragments: 'knowledge fragments',
      },
      KnowledgeBaseSettingsDialog: {
        edit_knowledge_base: 'Edit Knowledge Base',
        create_knowledge_base: 'Create Knowledge Base',
        knowledge_base_name: 'Knowledge Base Name',
        please_input_knowledge_base_name: 'Please enter knowledge base name',
        description: 'Description',
        optional_description_hint:
          'Optional, describe the purpose or content of the knowledge base',
        knowledge_base_name_at_least_2_characters:
          'Knowledge base name must be at least 2 characters',
        knowledge_base_name_cannot_exceed_100_characters:
          'Knowledge base name cannot exceed 100 characters',
        advanced_settings: 'Advanced Settings',
        visual_editing: 'Visual Editing',
        json_editing: 'JSON Editing',
        chunking_strategy: 'Chunking Strategy',
        chunking_strategy_hint: 'Default chunking strategy for document splitting',
        chunk_size: 'Chunk Size',
        chunk_size_hint: 'Maximum number of characters per document chunk',
        chunk_overlap: 'Chunk Overlap',
        chunk_overlap_hint: 'Number of overlapping characters between adjacent chunks',
        similarity_threshold: 'Similarity Threshold',
        similarity_threshold_hint: 'Minimum similarity threshold for search (0-1)',
        max_search_results: 'Max Search Results',
        max_search_results_hint: 'Maximum number of results returned per search',
        settings_json_format: 'Settings (JSON Format)',
        settings_json_format_hint:
          'Knowledge base settings in JSON format, leave blank to use default',
        format_json: 'Format JSON',
        reset_to_default: 'Reset to Default',
        confirm: 'Confirm',
        cancel: 'Cancel',
        please_input_valid_json_format: 'Please enter valid JSON format',
        json_format_success: 'JSON formatted successfully',
        invalid_json_format: 'Invalid JSON format, unable to format',
        reset_to_default_success: 'Reset to default settings',
        please_check_form_errors: 'Please check for errors in the form',
        save_failed: 'Failed to save, please try again',
      },
      KnowledgeDebug: {
        knowledge_base_debug_tool: 'Knowledge Base Debug Tool',
        database_status_check: 'Database Status Check',
        check_database_status: 'Check Database Status',
        check_chunk_data: 'Check Chunk Data',
        test_embedding: 'Test Embedding',
        test_text: 'Test Text',
        test_api_connection: 'Test API Connection',
        test_local_model: 'Test Local Model',
        embedding_result: 'Embedding Result',
        search_query: 'Search Query',
        select_knowledge_base: 'Select Knowledge Base',
        debug_search_pipeline: 'Debug Search Pipeline',
        test_search: 'Test Search',
        search_result: 'Search Result',
        document_id: 'Document ID',
        data_repair_tool: 'Data Repair Tool',
        database_status: 'Database Status',
        start_checking_database_status: 'Start checking database status...',
        database_status_check_completed: 'Database status check completed',
        database_status_check_failed: 'Database status check failed:',
        unknown_error: 'Unknown error',
        start_checking_chunk_data: 'Start checking chunk data...',
        chunk_data_check_completed: 'Chunk data check completed',
        chunk_data_check_failed: 'Chunk data check failed:',
        start_testing_embedding: 'Start testing embedding:',
        embedding_generation_completed: 'Embedding generation completed',
        embedding_generation_failed: 'Embedding generation failed:',
        start_testing_api_connection: 'Start testing API connection...',
        api_connection_test_success: 'API connection test successful',
        api_connection_test_failed: 'API connection test failed:',
        start_testing_local_model: 'Start testing local model...',
        local_model_test_completed: 'Local model test completed',
        local_model_test_failed: 'Local model test failed:',
        start_testing_search: 'Start testing search:',
        search_completed: 'Search completed, results:',
        search_failed: 'Search failed:',
        start_debugging_search_pipeline: 'Start debugging search pipeline...',
        search_pipeline_debug_completed: 'Search pipeline debug completed',
        search_pipeline_debug_failed: 'Search pipeline debug failed:',
        start_regenerating_document_chunks: 'Start regenerating document chunks:',
        document_chunks_regenerated: 'Document chunks regenerated:',
        document_chunks_regenerated_completed: 'Document chunks regeneration completed',
        document_chunks_regenerated_failed: 'Document chunks regeneration failed:',
        start_updating_document_embeddings: 'Start updating document embeddings:',
        document_embeddings_updated: 'Document embeddings updated:',
        document_embeddings_updated_completed: 'Document embeddings update completed',
        document_embeddings_updated_failed: 'Document embeddings update failed:',
        start_viewing_document_chunks: 'Start viewing document chunks:',
        document_chunks_viewed_completed: 'Document chunks view completed',
        document_chunks_viewed_failed: 'Document chunks view failed:',
        repair_result: 'Repair Result',
        regenerate_chunks: 'Regenerate Chunks',
        update_embeddings: 'Update Embeddings',
        view_document_chunks: 'View Document Chunks',
        debug_logs: 'Debug Logs',
        clear_logs: 'Clear Logs',
        logs_cleared: 'Logs Cleared',
        debug_tool_initialized: 'Debug Tool Initialized',
      },
      KnowledgeSearch: {
        placeholder: 'Search knowledge base content...',
        search: 'Search',
        scope: 'Search Scope',
        result_limit: 'Result Limit',
        min_score: 'Minimum Similarity',
        min_score_hint: 'Recommended 0.01-0.1',
        found_results: 'Found {count} related results',
        query: 'Query',
        no_result: 'No related content found',
        no_result_tips:
          'Try the following:<br />• Use different keywords<br />• Lower the similarity threshold to 0.01-0.05<br />• Check if the document has been vectorized<br />• Visit /debug page for diagnostics',
        retry_lower_threshold: 'Retry with lower threshold',
        debug_diagnose: 'Debug Diagnose',
        unnamed_document: 'Untitled Document',
        best: 'Best',
        better: 'Better',
        good: 'Good',
        bad: 'Bad',
        weak: 'Weak',
        knowledge_base: 'Knowledge Base',
        unknown: 'Unknown',
        similarity: 'Similarity',
        full_document: 'Full Document',
        loading_full_document: 'Loading full document...',
        notify_success: 'Search completed, found {count} related results',
        notify_failed: 'Search failed',
        notify_doc_failed: 'Failed to get full document content, showing fragment content',
        notify_success_caption: 'Document ID: {docId}, generated {chunkCount} knowledge fragments',
        notify_failed_caption: 'Document ID: {docId}',
        notify_success_message: 'Document vectorization completed',
        notify_failed_message: 'Document vectorization failed',
      },
      PexelsIntegration: {
        browser: 'Media Browser',
        selected: 'Selected Media',
        no_media_selected: 'No media selected',
        select_media_in_browser: 'Select images or videos in the media browser',
        clear_all: 'Clear All',
        export_selection: 'Export Selection',
        photo: 'Photo',
        video: 'Video {duration}',
        add_media_to_selection: '{media} added to selection',
        media_already_in_selection: 'Media already in selection',
        remove_media_from_selection: 'Removed from selection',
        clear_all_selection: 'All selections cleared',
        export_selection_success: '{count} media items exported',
      },
      PexelsMediaBrowser: {
        search_placeholder: 'Search images or videos...',
        search: 'Search',
        searching: 'Searching...',
        search_failed: 'Search failed',
        load_failed: 'Load failed',
        retry: 'Retry',
        try_different_keywords: 'Try searching with different keywords',
        please_input_search_keywords: 'Please enter search keywords',
        search_high_quality_media: 'Search for high-quality media content',
        input_keywords_search_pexels:
          'Enter keywords to search for free images and videos from Pexels',
        no_results: 'No results found',
        pagination_info: 'Showing {start} - {end} of {total} results',
        pexels_resource_provider_not_configured:
          'Pexels resource provider not configured, please configure in settings',
        pexels_api_config_incomplete: 'Pexels API configuration incomplete, please check settings',
      },
      SimpleEditor: {
        placeholder: 'Start typing... Markdown supported',
        unnamed_document: 'Untitled Document',
        get_title_failed: 'Failed to get title',
        get_markdown_failed: 'Failed to get Markdown',
        title: 'Title {level}',
        body: 'Body',
        paragraph: 'Paragraph',
      },
      ToolMessage: {
        operation: 'Operation',
        message: 'Message',
        match_info: 'Match Info',
        position: 'Position',
        search: 'Search',
        new_content: 'New Content',
        view_full_result: 'View Full Result',
        original_text: 'Original Text',
        parse_error: 'Parse Error',
        unknown: 'Unknown',
      },
      UploadDocumentDialog: {
        upload_document: 'Upload Document',
        file_upload: 'File Upload',
        drag_file_here: 'Drag file here or click to upload',
        supported_formats: 'Supported formats: TXT, MD, DOCX, PDF, HTML, JSON, XML, CSV, RTF',
        reselect: 'Reselect',
        document_info: 'Document Info',
        document_title: 'Document Title',
        please_input_document_title: 'Please enter document title',
        document_description: 'Document Description (optional)',
        upload: 'Upload Document',
        cancel: 'Cancel',
        file_validation_passed: 'File validation passed',
        file_size_too_large: 'File size cannot exceed 50MB',
        unsupported_file_format:
          'Unsupported file format. Please upload TXT, MD, DOCX, PDF, HTML, JSON, XML, CSV, or RTF files',
        please_select_file: 'Please select a file to upload',
        submit_failed: 'Submission failed, please try again',
      },
    },
    config: {
      llmProviders: {
        description: {
          anthropic: 'Anthropic Claude',
          azureOpenai: 'Azure OpenAI Service',
          deepseek: 'DeepSeek',
          gemini: 'Google Gemini',
          glm: 'Zhipu GLM',
          grok: 'xAI Grok',
          minimax: 'MiniMax Hailuo',
          moonshot: 'Moonshot KIMI',
          ollama: 'Ollama',
          openai: 'OpenAI GPT',
          qwen: 'Alibaba Qwen',
          volces: 'Volcengine Doubao',
        },
        name: {
          anthropic: 'Anthropic',
          azureOpenai: 'Azure OpenAI',
          deepseek: 'DeepSeek',
          gemini: 'Google Gemini',
          glm: 'GLM',
          grok: 'xAI Grok',
          minimax: 'MiniMax',
          moonshot: 'Moonshot',
          ollama: 'Ollama',
          openai: 'OpenAI',
          qwen: 'Qwen',
          volces: 'Volcengine Doubao',
        },
      },
      resourceMap: {
        description: {
          qwen: 'Qwen - Alibaba, Open Source',
          ollama: 'Ollama - Local Model Service, Support Multiple Models',
          minimax: 'MiniMax - Minimax, Support Multiple Models',
          deepseek: 'DeepSeek - Deep Learning Model, Support Multiple Models',
          volces: 'Volcengine Doubao - Volcengine Doubao Model',
          moonshot: 'Moonshot - Moonshot Model',
          anthropic: 'Anthropic - Claude Model',
          openai: 'OpenAI - GPT Model',
          azureOpenai: 'Azure OpenAI - Azure OpenAI Service',
          gemini: 'Google Gemini - Google Gemini Model',
          grok: 'Grok - xAI Grok Model',
          glm: 'Zhipu GLM - Zhipu GLM Model',
          pexels: 'Pexels - Pexels Image Library',
          tavily: 'Tavily - Tavily Search',
        },
        name: {
          qwen: 'Qwen',
          ollama: 'Ollama',
          minimax: 'MiniMax',
          deepseek: 'DeepSeek',
          volces: 'Volcengine Doubao',
          moonshot: 'Moonshot',
          anthropic: 'Anthropic',
          openai: 'OpenAI',
          azureOpenai: 'Azure OpenAI',
          gemini: 'Google Gemini',
          grok: 'Grok',
          glm: 'GLM',
          pexels: 'Pexels',
          tavily: 'Tavily',
        },
      },
    },
    layout: {
      MainLayout: {
        pleaseUseDesktop: 'Please use on desktop',
      },
    },
    utils: {
      clipboard: {
        successMessage: 'Copy Success',
        errorMessage: 'Copy Failed',
        checkBrowserPermission: 'Please check browser permission',
        browserNotSupported: 'Browser not support clipboard read',
      },
      editorFactory: {
        placeholder: 'Start typing...',
      },
      imageManager: {
        saveImageFailed: 'Save Image Failed',
        getImageFailed: 'Get Image Failed',
        getImageReferencesFailed: 'Get Image References Failed',
        processImageFileFailed: 'Process Image File Failed',
        processImageUrlFailed: 'Process Image URL Failed',
        downloadImageFailed: 'Download Image Failed',
        triggerImageAssociation: 'Trigger Image Association: {imageId} -> Document {documentId}',
        addImageReferenceMethodUnavailable: 'addImageReference Method Unavailable',
        triggerImageAssociationFailed: 'Trigger Image Association Failed: {error}',
        imageAssociationEventTriggered: 'Image Association Event Triggered: {imgId} -> {docId}',
        deleteUnreferencedImagesFailed: 'Delete Unreferenced Images Failed: {error}',
      },
      knowledgeBase: {
        markdown: {
          displayName: 'Markdown Chunker',
          description:
            'Structured chunking for Markdown documents, recognizing headers, code blocks, lists, and other Markdown elements',
          useCases:
            'Technical documents, README files, blog posts, project documents, API documentation, user manuals',
          advantages:
            'Preserves Markdown structure, recognizes header hierarchy, handles code blocks correctly, maintains list and table integrity, optimized semantic segmentation',
          limitations:
            'Only suitable for Markdown format, not effective for plain text, requires proper Markdown syntax',
          recommendedFor:
            'Documents with title structure, technical documentation, formatted text content, documents that need to maintain structure',
        },

        recursiveCharacter: {
          displayName: 'Recursive Character Chunker',
          description:
            'A generic text chunker that splits at natural boundaries (paragraphs, sentences, words), suitable for various text types',
          useCases:
            'Plain text documents, novels and literary works, news articles, email content, chat records, mixed format content',
          advantages:
            'Highly versatile, suitable for various text types, intelligent boundary recognition, maintains semantic integrity, recursive segmentation strategy, handles mixed text with Chinese and English',
          limitations:
            'Does not recognize specific format structures, has limited support for professional document formats, may split professional terms',
          recommendedFor:
            'Text without specific format, natural language content, dialogue and communication content, general document processing',
        },

        latex: {
          displayName: 'LaTeX Chunker',
          description:
            'A chunker specifically designed for LaTeX documents, preserving the integrity of mathematical formulas, environments, and commands',
          useCases:
            'Academic papers, mathematical documents, scientific reports, technical papers, teaching materials, research documents',
          advantages:
            'Preserves mathematical formula integrity, recognizes LaTeX environments, processes complex LaTeX commands, maintains theorem proof structure, suitable for academic content',
          limitations:
            'Only suitable for LaTeX format, requires proper LaTeX syntax, not effective for other formats',
          recommendedFor:
            'Documents with mathematical formulas, academic and research content, LaTeX source files, technical papers',
        },

        smart: {
          displayName: 'Smart Chunker',
          description:
            'Automatically detects content type and selects the most appropriate chunking strategy, supports automatic recognition of LaTeX, Markdown, and plain text',
          useCases:
            'Mixed format documents, unknown format content, batch document processing, automation workflows, multi-format knowledge bases, general document processing',
          advantages:
            'Automatic format detection, no need to manually select strategies, supports multiple formats, intelligent strategy selection, simplified usage process',
          limitations:
            'Detection may not be accurate, complex formats may be misjudged, rely on content feature recognition',
          recommendedFor:
            'Documents with uncertain format, automation processing scenarios, diverse content processing, rapid prototyping',
        },
      },
    },
    composeables: {
      useAnthropic: {
        toolExecutionFailed: 'Tool Execution Failed: {error}',
        errorOccurred: 'Sorry, an error occurred, please try again later.',
      },
      useAzureOpenAI: {
        toolExecutionFailed: 'Tool Execution Failed: {error}',
        errorOccurred: 'Sorry, an error occurred, please try again later.',
      },
      useConversition: {
        selectModel: 'Select Model',
        agent: 'Agent',
        agentDescription: 'Intelligent agent that can perform complex tasks',
        copilot: 'Copilot',
        copilotDescription: 'Can modify document content, name, etc.',
        chat: 'Chat',
        chatDescription:
          'Can read content information, but does not perform modification operations',
      },
      useDeepSeek: {
        toolExecutionFailed: 'Tool Execution Failed: {error}',
        errorOccurred: 'Sorry, an error occurred, please try again later.',
      },
      useFloatAgent: {
        errorOccurred: 'Sorry, an error occurred, please try again later.',
      },
      useGemini: {
        errorOccurred: 'Sorry, an error occurred, please try again later.',
        unknownError: 'Unknown Error',
      },
      useGlm: {
        errorOccurred: 'Sorry, an error occurred, please try again later.',
        unknownError: 'Unknown Error',
      },
      useGrok: {
        errorOccurred: 'Sorry, an error occurred, please try again later.',
      },
      useKnowledge: {
        knowledgeBaseCreated: 'Knowledge Base Created',
        createKnowledgeBaseFailed: 'Create Failed',
        knowledgeBaseUpdated: 'Knowledge Base {name} Updated',
        updateKnowledgeBaseFailed: 'Update Knowledge Base Failed',
        knowledgeBaseDeleted: 'Knowledge Base Deleted',
        deleteKnowledgeBaseFailed: 'Delete Knowledge Base Failed',
        getKnowledgeBaseEmptyData: 'Get Knowledge Base Detail Failed',
        loadKnowledgeBaseDetailFailed: 'Load Knowledge Base Detail Failed',
        knowledgeDocumentAdded: 'Knowledge Document Added',
        createKnowledgeDocFailed: 'Create Knowledge Document Failed',
        knowledgeDocumentUpdated: 'Knowledge Document Updated',
        updateKnowledgeDocFailed: 'Update Knowledge Document Failed',
        knowledgeDocumentDeleted: 'Knowledge Document Deleted',
        deleteKnowledgeDocFailed: 'Delete Knowledge Document Failed',
        addDocumentToKBFailed: 'Add Document to Knowledge Base Failed',
        documentRemovedFromKB: 'Document Removed from Knowledge Base',
        removeDocumentFromKBFailed: 'Remove Document from Knowledge Base Failed',
        searchKnowledgeFailed: 'Search Knowledge Base Failed',
        searchFailed: 'Search Failed: {error}',
        batchSearchCompleted:
          'Batch Search Completed, Found {totalResults} related results in {knowledgeBaseIdsLength} knowledge bases',
        batchSearchNoResults:
          'No related content found in {knowledgeBaseIdsLength} knowledge bases',
        batchSearchFailed: 'Batch Search Knowledge Base Failed',
        batchSearchFailedMessage: 'Batch Search Knowledge Base Failed: {error}',
        getKnowledgeBaseStatsFailed: 'Get Knowledge Base Stats Failed',
        searchTimeout: 'Search Timeout',
        searchTimeoutCaption:
          'Possible vectorization configuration issue, downgraded to text matching search',
        embeddingGenerated: 'Embedding Generated Success: {dimension}D',
        embeddingGeneratedFailed: 'Embedding Generated Failed: {error}',
        embeddingGeneratedError: 'Embedding Generated Error: {error}',
        chunkVectorUpdated: 'Chunk Vector Updated Success: {message}',
        chunkVectorUpdatedCaption: 'Updated Chunks: {updated_chunks}',
        chunkVectorUpdatedFailed: 'Chunk Vector Update Failed: {error}',
        chunkVectorUpdatedError: 'Chunk Vector Update Error: {error}',
        embeddingApiTestSuccess: 'Embedding API Test Success',
        embeddingApiTestSuccessCaption: 'Dimension: {dimension}',
        embeddingApiTestFailed: 'Embedding API Test Failed: {error}',
        embeddingApiTestError: 'Error testing embedding API',
        multiSemanticEmbeddingTestSuccess: 'Multi-Semantic Embedding Test Success',
        multiSemanticEmbeddingTestSuccessCaption:
          'Strategy: {processing_strategy}, Dimension: {embedding_dimension}',
        multiSemanticEmbeddingTestFailed: 'Multi-Semantic Embedding Test Failed: {error}',
        multiSemanticEmbeddingTestError: 'Error testing multi-semantic embedding',
        documentChunksRegenerated: 'Document Chunks Regenerated Success: {message}',
        documentChunksRegeneratedCaption:
          'Deleted Old Chunks: {old_chunks}, Created New Chunks: {new_chunks}',
        documentChunksRegenerationFailed: 'Document Chunks Regeneration Failed: {error}',
        objectBoxConnectionTestSuccess: 'ObjectBox Connection Test Success: {details}',
        objectBoxConnectionTestFailed: 'ObjectBox Connection Test Failed: {error}',
        objectBoxConnectionTestError: 'ObjectBox Connection Test Error: {error}',
      },
      useMiniMax: {
        toolExecutionFailed: 'Tool Execution Failed: {error}',
        errorOccurred: 'Sorry, an error occurred, please try again later.',
      },
      useMoonshot: {
        toolExecutionFailed: 'Tool Execution Failed: {error}',
        errorOccurred: 'Sorry, an error occurred, please try again later.',
      },
      useOllama: {
        toolExecutionFailed: 'Tool Execution Failed: {error}',
        errorOccurred: 'Sorry, an error occurred, please try again later.',
      },
      useOpenAI: {
        toolExecutionFailed: 'Tool Execution Failed: {error}',
        errorOccurred: 'Sorry, an error occurred, please try again later.',
      },
      useQwen: {
        toolExecutionFailed: 'Tool Execution Failed: {error}',
        errorOccurred: 'Sorry, an error occurred, please try again later.',
      },
      useVolces: {
        toolExecutionFailed: 'Tool Execution Failed: {error}',
        errorOccurred: 'Sorry, an error occurred, please try again later.',
      },
      useKnowledgeDocumentBatch: {
        create_document_failed: 'Failed to Create Knowledge Base Document',
        submit_chunking_result_failed: 'Failed to Submit Chunking Result',
        chunking_failed: 'Document Chunking Failed',
        document_vectorized_successfully: 'Document Vectorization Completed',
        document_vectorized_caption:
          'Document ID: {docId}, Generated {chunkCount} Knowledge Fragments',
        document_vectorization_failed: 'Document Vectorization Failed',
        document_id: 'Document ID',
        processing_document: 'Processing Document: {title}',
        document_processed_successfully: 'Document Processed Successfully: {title}',
        batch_processing_completed:
          'Batch Processing Completed, Successfully Processed {successCount} Documents',
        batch_processing_failed: 'Batch Processing Failed',
        no_documents_to_process: 'No Documents Found to Process',
        get_folder_documents_failed: 'Failed to Get Folder Documents',
      },
      useDocumentChunking: {
        chunking_completed: 'Document "{title}" Chunking Completed, {count} Chunks Generated',
        chunking_failed: 'Document "{title}" Chunking Failed: {error}',
        process_failed: 'Processing Document "{title}" Failed: {error}',
        vectorization_completed:
          'Document {docId} Vectorization Completed, Processed {chunkCount} Chunks',
        vectorization_failed: 'Document {docId} Vectorization Failed',
      },
      useDocumentToKnowledge: {
        batch_completed: 'Batch process result：{processed}/{total}，failed {errors} documents',
      },
    },
    composables: {
      useErrorHandler: {
        retry: 'Retry',
        close: 'Close',
        editorLoad: 'Editor Load Failed, Please Refresh the Page',
        contentSync: 'Content Sync Failed, Your Changes May Not Be Saved',
        dragOperation: 'Drag Operation Failed, Please Try Again',
        fileTree: 'File Tree Load Failed, Please Refresh the Page',
        saveOperation: 'Save Failed, Please Check the Network Connection and Try Again',
        operationFailed: 'Operation Failed, Please Try Again',
      },
      useKnowledgeBaseManager: {
        loadKnowledgeBaseFailed: 'Load Knowledge Base Failed',
        knowledgeBaseCreated: 'Knowledge Base {name} Created Success',
        knowledgeBaseCreationFailed: 'Create Knowledge Base Failed',
        knowledgeBaseUpdated: 'Knowledge Base {name} Updated Success',
        knowledgeBaseUpdateFailed: 'Update Knowledge Base Failed',
        knowledgeBaseDeleted: 'Knowledge Base Deleted',
        knowledgeBaseDeletionFailed: 'Delete Knowledge Base Failed',
        documentDeleted: 'Document Deleted Success',
        documentVectorized: 'Document Vectorized Success',
        documentVectorizedCaption: 'Document ID: {docId}, Generated {chunkCount} knowledge chunks',
        documentVectorizationFailed: 'Document Vectorization Failed',
        documentVectorizationFailedCaption: 'Document ID: {docId}',
        documentDeletionFailed: 'Delete Document Failed',
        documentDeletionFailedCaption: 'Document ID: {docId}',
      },
      useLoadingState: {
        loading: 'Loading...',
        loadingTimeout: 'Loading Timeout',
        operationFailed: 'Operation Failed',
      },
    },
    llm: {
      tools: {
        editor: {
          findAllMatches: {
            foundMatches: 'Found {count} matches',
            noMatches: 'No matches found for "{searchText}"',
            error: 'Search Failed: {error}',
          },
          verifyOperation: {
            missingParams: 'Missing Verification Parameters',
            replaceAddedTrackChange:
              'Replace Operation Added TrackChange Mark, Waiting for User Confirmation',
            replaceAddedTrackChangeDetails:
              'Text "{originalText}" has been marked as replaced, waiting for user confirmation to take effect',
            replaceCompleted: 'Replace Operation Completed',
            replaceCompletedDetails:
              'Original text "{originalText}" has been marked as deleted, new text "{newText}" has been marked as inserted',
            replaceFailed: 'Replace Operation Verification Failed',
            replaceFailedDetails: 'Unable to find the replaced text "{newText}" in the document',
            missingInsertText: 'Missing Insert Text Parameter',
            insertCompleted: 'Insert Operation Completed',
            insertCompletedDetails: 'Text "{newText}" has been successfully inserted',
            insertFailed: 'Insert Operation Verification Failed',
            insertFailedDetails: 'Unable to find the inserted text "{newText}" in the document',
            missingDeleteText: 'Missing Delete Text Parameter',
            deleteAddedTrackChange:
              'Delete Operation Added TrackChange Mark, Waiting for User Confirmation',
            deleteAddedTrackChangeDetails:
              'Text "{originalText}" has been marked as deleted, waiting for user confirmation to take effect',
            deleteCompleted: 'Delete Operation Completed',
            deleteCompletedDetails: 'Text "{originalText}" has been deleted',
            unknownOperation: 'Unknown Operation Type',
            error: 'Error Occurred During Verification',
            searchAndReplace: {
              noEditorInstance: 'No Editor Instance Found for Document ID {documentId}',
              noActiveEditor:
                'No Active Editor Instance Found, Please Ensure There is an Open Document',
              noMatches: 'No matches found for "{searchText}"',
              occurrenceOutOfRange:
                'The specified match index {occurrence} is out of range, found {totalMatches} matches (index starts from 0)',
              multipleMatches:
                'Found {totalMatches} matches, please specify the occurrence parameter to replace: \n{matchList}',
              deleteComment: 'AI Suggested Delete: "{targetMatchText}"',
              insertComment: 'AI Suggested Insert: "{replaceText}"',
              replaceCompleted: 'Successfully replaced "{searchText}" with "{replaceText}"',
              replaceFailed: 'Replace Operation Failed, Please Try Again',
              error: 'Error Occurred During Replace Operation: {error}',
            },
            searchAndInsert: {
              noEditorInstance: 'No Editor Instance Found for Document ID {documentId}',
              noActiveEditor:
                'No Active Editor Instance Found, Please Ensure There is an Open Document',
              noMatches: 'No matches found for "{searchText}"',
              occurrenceOutOfRange:
                'The specified match index {occurrence} is out of range, found {totalMatches} matches (index starts from 0)',
              multipleMatches:
                'Found {totalMatches} matches, please specify the occurrence parameter to insert: \n{matchList}',
              positionOutOfRange:
                'Character position {position} is out of range, search text length is {searchTextLength}',
              invalidPosition:
                'Invalid Insert Position, Please Use "before", "after" or Number Index',
              insertComment: 'AI Suggested Insert: "{insertText}"',
              insertCompleted:
                'Successfully inserted "{insertText}" at {positionDesc} of "{searchText}"',
              insertFailed: 'Insert Operation Failed, Please Try Again',
              error: 'Error Occurred During Insert Operation: {error}',
              before: 'Before',
              after: 'After',
              positionDesc: 'The {position}th character position',
            },
            searchAndDelete: {
              noEditorInstance: 'No Editor Instance Found for Document ID {documentId}',
              noActiveEditor:
                'No Active Editor Instance Found, Please Ensure There is an Open Document',
              noMatches: 'No matches found for "{searchText}"',
              occurrenceOutOfRange:
                'The specified match index {occurrence} is out of range, found {totalMatches} matches (index starts from 0)',
              multipleMatches:
                'Found {totalMatches} matches, please specify the occurrence parameter to delete: \n{matchList}',
              deleteComment: 'AI Suggested Delete: "{targetMatchText}"',
              deleteCompleted: 'Successfully deleted "{searchText}"',
              deleteFailed: 'Delete Operation Failed, Please Try Again',
              error: 'Error Occurred During Delete Operation: {error}',
            },
          },
          common: {
            noEditorInstance: 'No Editor Instance Found for Document ID {documentId}',
            noActiveEditor:
              'No Active Editor Instance Found, Please Ensure There is an Open Document',
          },
          getDocumentInfo: {
            success: 'Successfully Retrieved Document Information',
            cannotDetermineWindow: 'Cannot Determine Document Window Information',
            error: 'Error Occurred During Get Document Info: {error}',
          },
          hasPendingChanges: {
            found: 'Found {count} pending AI modifications',
            none: 'No pending AI modifications',
            error: 'Error Occurred During Check Modifications: {error}',
          },
          acceptAllChanges: {
            noEditorInstance: 'No Editor Instance Found for Document ID {documentId}',
            noActiveEditor:
              'No Active Editor Instance Found, Please Ensure There is an Open Document',
            success: 'Accepted {count} AI modifications',
            failed: 'Accept Modifications Failed, Please Try Again',
            error: 'Error Occurred During Accept Modifications: {error}',
          },
          rejectAllChanges: {
            noEditorInstance: 'No Editor Instance Found for Document ID {documentId}',
            noActiveEditor:
              'No Active Editor Instance Found, Please Ensure There is an Open Document',
            success: 'Rejected {count} AI modifications',
            failed: 'Reject Modifications Failed, Please Try Again',
            error: 'Error Occurred During Reject Modifications: {error}',
          },
          formatAndResetDocument: {
            noEditorInstance: 'No Editor Instance Found for Document ID {documentId}',
            noActiveEditor:
              'No Active Editor Instance Found, Please Ensure There is an Open Document',
            emptyContent: 'Format Content Cannot Be Empty',
            success:
              'Document Formatting Completed, Content Updated from {originalLength} characters to {newLength} characters',
            verified: 'Document Content Successfully Reset to Formatted Content',
            details:
              'Original Document Length: {originalLength} characters, New Document Length: {newLength} characters',
            failed: 'Document Formatting Failed, Please Try Again',
            error: 'Error Occurred During Document Formatting: {error}',
          },
          searchText: {
            error: 'Search Failed: {error}',
          },
        },
        file: {
          unnamedFolder: 'Unnamed Folder',
          unnamedDocument: 'Unnamed Document',
          getFileTree: {
            success:
              'Successfully Retrieved File Tree Structure, Found {folderCount} folders and {documentCount} documents; Full Structure Data: {detail}',
            failed: 'Get File Tree Failed: {error}',
          },
          createDocument: {
            emptyTitle: 'Document Title Cannot Be Empty',
            folderNotFound: 'Folder Not Found with ID {folderId}',
            success: 'Document "{title}" in Folder "{folderName}" was Created Successfully',
            error: 'Error Occurred During Create Document: {error}',
          },
          deleteDocuments: {
            emptyIds: 'Document ID Array Cannot Be Empty',
            idsNotArray: 'documentIds must be an array format',
            notFound: 'Document Not Found with IDs: {ids}',
            noneFound: 'No Documents Found to Delete',
            success: 'Batch Delete Completed',
            error: 'Error Occurred During Batch Delete Documents: {error}',
          },
          renameDocument: {
            emptyTitle: 'New Document Title Cannot Be Empty',
            notFound: 'Document Not Found with ID {documentId}',
            sameTitle: 'New Title is the Same as the Current Title, No Need to Modify',
            success:
              'Document "{oldTitle}" was renamed to "{newTitle}" successfully, details: {details}',
            error: 'Error Occurred During Rename Document: {error}',
          },
          searchDocuments: {
            emptyText: 'Search Text Cannot Be Empty',
            success:
              'Search "{searchText}" Completed, Found {count} Matching Results, Details: {details}',
            error: 'Search Failed: {error}',
          },
          createFolder: {
            emptyName: 'Folder Name Cannot Be Empty',
            parentNotFound: 'Parent Folder Not Found with ID {parentId}',
            success:
              'Folder "{name}" was created, foler id: {folderId}, parent folder name: "{parentName}", parent folder id: {parentId}',
            error: 'Error Occurred During Create Folder: {error}',
          },
          deleteFolder: {
            notFound: 'Folder Not Found with ID {folderId}',
            hasChildren:
              'Cannot Delete Folder "{folderName}" because it contains {count} sub-items. This is a high-risk operation, please handle it by user self.',
            success: 'Folder "{folderName}" was successfully deleted.',
            error: 'Error Occurred During Delete Folder: {error}',
          },
          renameFolder: {
            emptyName: 'New Folder Name Cannot Be Empty',
            notFound: 'Folder Not Found with ID {folderId}',
            sameName: 'New Name is the Same as the Current Name, No Need to Modify',
            success: 'Folder "{oldName}" was renamed to "{newName}"',
            error: 'Error Occurred During Rename Folder: {error}',
          },
          openDocument: {
            noDocumentId: 'No Document ID Specified',
            notFound: 'Document Not Found with ID {documentId}',
            noFolder: 'Document {documentId} has no associated folder',
            folderMismatch:
              'Document {documentId} is not in the specified folder {folderId}, actually in folder {actualFolderId}',
            folderNotFound: 'Folder Not Found with ID {folderId}',
            documentNotFound: 'Document Not Found in Folder "{folderName}" with ID {documentId}',
            success: 'Document was successfully opened: id - {documentId}, title - {title}',
            error: 'Error Occurred During Open Document: {error}',
          },
          updateDocumentContent: {
            emptyContent: 'Document Content Parameter Cannot Be Undefined or Null',
            notFound: 'Document Not Found with ID {documentId}',
            success: 'Document Content was successfully updated',
            error: 'Error Occurred During Update Document Content: {error}',
          },
          searchFolders: {
            emptyKeyword: 'keywords cannot be empty',
            success: 'searched {count} folders by keywords "{keyword}", details: {details}',
            error: 'Error Occurred During Search folders: {error}',
          },
        },
        knowledgeBase: {
          noKnowledgeBaseSelected:
            'No Knowledge Base Selected, Please Select a Knowledge Base in the Conversation Interface First, or Specify knowledge_base_id in the Tool Parameters',
          searchResult:
            'Search in Knowledge Base "{knowledgeBaseName}" for "{query}", Found {resultCount} related results:\n\n',
          noResult:
            'No related content found in Knowledge Base "{knowledgeBaseName}" for "{query}".',
          resultItem:
            '**{index}. {title}**\nSimilarity: {score}%\nContent Summary: {summary}{ellipsis}\n\n',
          relatedContent: 'Related Content',
          resultFooter:
            "The above is related information retrieved from the knowledge base, please answer the user's question based on these contents.",
          error: 'Knowledge Base Search Failed: {error}',
          knowledgeBaseName: 'Knowledge Base {id}',
        },
        pexels: {
          noPexelsResourceProvider:
            'Pexels Resource Provider Not Configured, Please Configure in Settings',
          pexelsApiCredentialsNotConfigured:
            'Pexels API Credentials Not Configured, Please Configure in Settings',
          pexelsApiRequestFailed: 'Pexels API Request Failed: {status}, {statusText}',
          successfullyFetchedImages: 'Successfully Fetched {count} Images, Search Keyword: {query}',
          pexelsSearchFailed: 'Pexels Search Failed: {error}',
          pexelsVideoApiRequestFailed: 'Pexels Video API Request Failed: {status}, {statusText}',
          pexelsVideoSearchFailed: 'Pexels Video Search Failed: {error}',
          successfullyFetchedVideos: 'Successfully Fetched {count} Videos, Search Keyword: {query}',
        },
        search: {
          noTavilyResourceProvider:
            'Tavily Search Engine Not Configured, Please Configure in Settings',
          tavilyApiKeyNotConfigured:
            'Tavily API Key Not Configured, Please Configure Your Tavily API Key in Settings',
          searchRequestFailed: 'Search Request Failed: {status}, {statusText}',
          searchCompleted: 'Search Completed, Found {count} Results',
          searchFailed: 'Search Failed: {error}',
          directAnswer: 'Direct Answer: {answer}',
        },
      },
    },
  },
};
