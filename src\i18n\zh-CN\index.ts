export default {
  base: '应用设置',
  editor: '编辑器',
  llm: '大模型供应商',
  searchEngine: '搜索引擎',
  resourceProvider: '资源服务商',
  knowledgeBase: '知识库',
  settings: {
    title: '设置',
    applicationSettings: '应用设置',
    interfaceLanguage: '界面语言',
    selectLanguage: '选择语言',
    languageDescription: '更改界面显示语言，设置将自动保存',
    languageSaved: '语言设置已保存',
    languageSaveFailed: '语言设置失败',
    selectTheme: '选择主题',
  },
  theme_light: '浅色',
  theme_dark: '深色',
  fontSize: '字体大小',
  lineHeight: '行高',
  enableAutoComplete: '启用自动补全',
  autoComplete: '自动补全',
  times: '倍',

  newFolder: '新建文件夹',
  newDocument: '新建文档',

  appInitializing: '应用初始化中...',
  qtConnecting: '正在连接Qt环境...',
  qtApiInitializing: '正在初始化Qt API...',
  initCompleted: '初始化完成',
  appLoading: '应用载入中...',
  initField: '初始化失败，继续加载...',
  enableEmbeddTitle: '显示嵌入式标题',
  enableToolbar: '显示工具栏',
  toolbar: '工具栏',

  show: '显示',
  hide: '隐藏',

  copiedItem: '副本',
  configuration: '配置',
  formate: '格式',
  copySuccess: '复制成功',
  copyFailed: '复制失败',

  minimax: '海螺AI',
  iKnownIt: '知道了',
  ollama: 'Ollama',
  openai: 'OpenAI',
  doubao: '豆包大模型',

  default: '默认',
  dont_touch_ollama_api_key: '请勿修改 Ollama 的ApiKey',

  src: {
    components: {
      // Tiptap Editor Components
      tiptap: {
        knowledgeSearch: '知识库',
        pexelsIntegration: '媒体库',
        // Editor Toolbar
        editorToolbar: {
          // 工具栏按钮提示
          bold: '粗体',
          italic: '斜体',
          underline: '下划线',
          strikethrough: '删除线',
          code: '代码',
          codeBlock: '代码块',
          paragraph: '段落',
          heading1: '标题 1',
          heading2: '标题 2',
          heading3: '标题 3',
          heading4: '标题 4',
          heading5: '标题 5',
          heading6: '标题 6',
          bulletList: '无序列表',
          orderedList: '有序列表',
          taskList: '任务列表',
          blockquote: '引用',
          horizontalRule: '分隔线',
          hardBreak: '换行',
          alignLeft: '左对齐',
          alignCenter: '居中对齐',
          alignRight: '右对齐',
          alignJustify: '两端对齐',
          undo: '撤销',
          redo: '重做',
          link: '链接',
          image: '图片',
          table: '表格',
          emoji: '表情',
          highlight: '高亮',
          color: '颜色',
          fontSize: '字体大小',
          fontFamily: '字体',
          clearFormat: '清除格式',
          ai: 'AI助手',
          replace: '替换',
          find: '查找',
          wordCount: '字数统计',

          // 颜色选择器
          textColor: '文字颜色',
          backgroundColor: '背景颜色',

          // 表格操作
          addColumnBefore: '在左侧插入列',
          addColumnAfter: '在右侧插入列',
          deleteColumn: '删除列',
          addRowBefore: '在上方插入行',
          addRowAfter: '在下方插入行',
          deleteRow: '删除行',
          deleteTable: '删除表格',
          mergeCells: '合并单元格',
          splitCell: '拆分单元格',
          toggleHeaderColumn: '切换标题列',
          toggleHeaderRow: '切换标题行',
          toggleHeaderCell: '切换标题单元格',
        },

        // Word Count Stats
        wordCountStats: {
          title: '文档统计',
          selectDocument: '请选择一个文档以查看统计信息',
          wordCount: '字数统计',
          characterCount: '字符数',
          paragraphCount: '段落数',
          readingTime: '阅读时间(分钟)',
          documentId: '文档ID',
          activePanel: '活动面板',
          lastUpdated: '最后更新',
        },

        // Document Metadata
        documentMetadata: {
          title: '文档信息',
          metadata: '元数据',
          properties: '属性',
          tags: '标签',
          category: '分类',
          createdAt: '创建时间',
          updatedAt: '更新时间',
          wordCount: '字数',
          readingTime: '阅读时间',
          keywords: '关键词',
          description: '描述',
          addTag: '添加标签',
          addKeyword: '添加关键词',
          selectDocument: '请选择一个文档以查看信息',
          loading: '加载中...',
          documentId: '文档ID',
          noTitle: '无标题',
          prompt: '提示词',
          note: '备注',
          promptHint: '用于AI生成的提示词，可以帮助模型更好地理解文档内容',
          noteHint: '对文档的额外说明或备注',
          saving: '正在保存...',
          metadataSaved: '元数据已保存',
          saveMetadataFailed: '保存元数据失败',
          parseMetadataFailed: '解析元数据失败',
          loadFailed: '加载失败',
          unknown: '未知',
          locale: 'zh-CN',
        },

        // Search & Replace
        searchReplace: {
          title: '查找与替换',
          search: '查找',
          replace: '替换',
          replaceAll: '全部替换',
          caseSensitive: '区分大小写',
          wholeWord: '全词匹配',
          useRegex: '使用正则表达式',
          noResults: '未找到匹配内容',
          results: '找到 {count} 个匹配项',
          confirmReplaceAll: '确认替换所有匹配项？',
          replaceSuccess: '已成功替换 {count} 个匹配项',
        },

        // Translation Tool
        translationTool: {
          title: '翻译工具',
          translate: '翻译',
          sourceLanguage: '源语言',
          targetLanguage: '目标语言',
          autoDetect: '自动检测',
          copyResult: '复制结果',
          insertToEditor: '插入到编辑器',
          replaceSelection: '替换选中内容',
          translating: '翻译中...',
          translationComplete: '翻译完成',
          translationFailed: '翻译失败，请重试',

          // 输入字段
          originalContent: '原始内容',
          enterContentToTranslate: '请输入需要翻译的内容...',
          domainHint: '领域提示（可选）',
          domainHintPlaceholder: '例如：技术文档、法律文件、医学文献等',
          domainHintTooltip: '提供领域信息可以提高翻译质量',
          targetLanguageLabel: '目标语言',
          startTranslation: '开始翻译',

          // 结果区域
          translationResults: '翻译结果：',
          clickOptionToCopy: '点击任意选项复制到剪贴板',
          copy: '复制',

          // 错误消息
          enterContent: '请输入需要翻译的内容',
          selectTargetLanguage: '请选择目标语言',
          configureApiKey: '请先在设置中配置通义千问API Key',
          translationFailedGeneral: '翻译失败，请检查网络连接和API配置',
          translationRequestFailed: '翻译请求失败',
          translationResponseInvalid: '翻译响应格式异常',

          // 通知消息
          translationSuccessful: '翻译完成',
          copySuccessful: '复制成功',
          copyFailed: '复制失败',

          // 语言选项
          languages: {
            english: '英语',
            chinese: '中文',
            japanese: '日语',
            korean: '韩语',
            french: '法语',
            german: '德语',
            spanish: '西班牙语',
            arabic: '阿拉伯语',
            russian: '俄语',
            italian: '意大利语',
            portuguese: '葡萄牙语',
            dutch: '荷兰语',
            thai: '泰语',
            vietnamese: '越南语',
            indonesian: '印尼语',
            turkish: '土耳其语',
            polish: '波兰语',
            czech: '捷克语',
            hungarian: '匈牙利语',
            swedish: '瑞典语',
            finnish: '芬兰语',
            hebrew: '希伯来语',
            hindi: '印地语',
            bengali: '孟加拉语',
            malay: '马来语',
            cantonese: '粤语',
          },
        },

        // Code Block
        codeBlock: {
          language: '语言',
          copyCode: '复制代码',
          copied: '已复制',
          runCode: '运行代码',
          editCode: '编辑代码',
          deleteCodeBlock: '删除代码块',
          copyFailed: '复制失败',
        },

        // Excalidraw
        excalidraw: {
          title: '绘图工具',
          drawing: '绘图',
          save: '保存绘图',
          cancel: '取消',
          clear: '清空画布',
          export: '导出图片',
          import: '导入文件',
        },

        // Mermaid
        mermaid: {
          title: '图表工具',
          diagram: '图表',
          code: '代码',
          preview: '预览',
          save: '保存图表',
          cancel: '取消',
          edit: '编辑代码',
          undo: '撤销',
          redo: '重做',
          viewLargeImage: '查看大图',
          copy: '复制',
          enterCode: '输入Mermaid代码...',
          renderError: '渲染错误',
          renderingChart: '正在渲染图表...',
          codeCopied: '代码已复制',
          chartCopied: '图表已复制',
          copyFailed: '复制失败',
          failedToLoadMermaid: '无法加载Mermaid库',
          renderFailed: '渲染失败',
          largePreview: 'Mermaid 图表预览',
          types: {
            flowchart: '流程图',
            sequence: '时序图',
            class: '类图',
            state: '状态图',
            entity: '实体关系图',
            userJourney: '用户旅程图',
            gantt: '甘特图',
            pie: '饼图',
            quadrant: '象限图',
            requirement: '需求图',
            gitgraph: 'Git图',
            c4: 'C4模型',
          },
        },

        // AI Change Action Bar
        aiChangeActionBar: {
          accept: '接受',
          reject: '拒绝',
          acceptAll: '全部接受',
          rejectAll: '全部拒绝',
          aiSuggestion: 'AI建议',
          processing: '处理中...',
          acceptTooltip: '接受所有AI建议的修改',
          rejectTooltip: '拒绝所有AI建议的修改',
          viewDetails: '查看详情',
          changeDetails: '修改详情',
          insertion: '新增',
          deletion: '删除',
        },

        // TipTap Main Component
        tipTap: {
          saveFailedRetry: '保存失败，点击重试',
          reSave: '重新保存',
          save: '保存',
          saving: '保存中...',
          pendingSave: '待保存...',
          hideCatalog: '隐藏目录',
          showCatalog: '显示目录',
          enterDocumentTitle: '请输入文档标题',
          startTypingContent: '开始输入内容...',
        },

        // Power Edge
        powerEdge: {
          title: '边缘工具',
          quickActions: '快捷操作',
          aiAssistant: 'AI助手',
          format: '格式化',
          analyze: '分析',
          summarize: '总结',
          suggest: '建议',
        },
      },
      ConversitonContainer: {
        label: {
          new_conversation: '新对话',
        },
        menu: {
          reset_font_size: '重设字号',
          increase_font_size: '文字放大',
          decrease_font_size: '文字缩小',
        },
        knowledge: {
          relevance: '相关度',
          knowledge_search_results: '知识库搜索结果',
          knowledge_search_results_count:
            '在知识库 "{knowledgeBaseName}" 中找到 {count} 个相关结果',
        },
        ai_question: {
          title: 'AI 正在向你提问',
          input_placeholder: '请输入你的回答',
          submit_answer: '提交回答',
          cancel_question: '取消提问',
        },
        input: {
          placeholder_conversation: '输入消息...',
          placeholder_document: '输入消息开始文档对话...',
          placeholder_new: '输入消息开始新对话...',
        },
        tooltip: {
          selected_knowledge_base: '已选择知识库',
          no_knowledge_base: '未选择知识库',
          selected_text_preview: '选中文本预览',
          document_id: '文档ID',
          position: '位置',
          add_attachment: '添加附加内容',
          upload_file: '上传文件',
          upload_image: '上传图片',
          paste_clipboard: '粘贴剪贴板',
          upload_file_hint: '支持图片、文档等文件',
          upload_image_hint: '支持 JPG、PNG、GIF 等格式',
          paste_clipboard_hint: '粘贴文本或图片',
          select_model: '选择大模型',
          select_prompt: '选择提示词',
          abort_conversation: '中断对话',
          aborting: '正在中断...',
          send_message: '发送消息',
          send_message_and_create_new_conversation: '发送消息并创建新对话',
          prompt: '提示词',
          text_snippet: '片段',
          document: '文档',
          image: '图片',
          file: '文件',
        },
        button: {
          llm_settings: '大模型设置',
        },
        warning: {
          no_tool_support: '当前模型不支持工具调用，只能进行对话交流',
        },
        notification: {
          answer_cancelled: '已取消回答',
          cancel_failed: '取消失败',
          please_input_answer: '请输入回答内容',
          answer_submitted: '回答已提交',
          submit_failed: '提交失败，请重试',
          conversation_aborted: '您已中断了当前回复',
          abort_failed: '中断失败',
          file_added: '文件"{fileName}"已添加到附加内容',
          image_added: '图片"{fileName}"已添加到附加内容',
          clipboard_image_added: '剪贴板图片已添加到附加内容',
          clipboard_text_added: '剪贴板文本已添加到附加内容',
          clipboard_empty: '剪贴板中没有可用的内容',
          clipboard_read_failed: '读取剪贴板失败，请检查浏览器权限',
        },
      },
      settings: {
        // Settings panel
        SettingPannel: {
          base: '应用设置',
          llm: '大模型供应商',
          editor: '编辑器',
          knowledgeBase: '知识库',
          searchEngine: '搜索引擎',
          resourceProvider: '资源服务商',
          autoComplete: '自动补全',
          floatAgent: '浮动智能体',
          prompt: '提示词',
        },

        // LLM Settings
        LlmSettings: {
          title: '大模型设置',
          description: '配置和管理您的大语言模型供应商',
          providers: '大模型供应商',
          currentProvider: '当前供应商',
          enabled: '已启用',
          disabled: '已禁用',
          noProviderSelected: '请选择供应商进行配置',
          enableRequired: '您至少应该启用一个大模型供应商',
        },

        // Knowledge Base Settings
        KnowledgeBaseSettings: {
          title: '知识库配置',
          description: '配置嵌入式模型和向量化参数',
          embeddingMode: '嵌入模式',
          cloudApi: '云端API',
          localModel: '本地模型',
          autoSelect: '自动选择',
          selectProvider: '选择供应商',
          selectEmbeddingModel: '选择嵌入模型',
          embeddingDimension: '向量维度',
          embeddingDimensionHint: '自动检测的embedding模型向量维度，用于HNSW索引配置',
          refreshDimension: '重新检测维度',
          baseUrl: 'API地址',
          apiKey: 'API密钥',
          model: '模型',
          testConnection: '测试连接',
          connectionSuccess: '连接成功',
          connectionFailed: '连接失败',
          localWarning:
            '为确保知识库的准确性和本地应用效率，请选择效果足够好的嵌入式大模型，如果您不是使用NVIDIA显卡或显存低于16G，请使用云端API进行向量化，否则可能有损您的使用体验！',

          // 本地GGUF配置
          localGGUFConfig: '本地GGUF模型配置',
          modelFilePath: '模型文件路径',
          modelFilePathHint: '选择本地GGUF格式的嵌入式模型文件',
          gpuLayers: 'GPU层数',
          gpuLayersHint: '使用GPU加速的层数，0为仅CPU',
          contextSize: '上下文大小',
          contextSizeHint: '模型的上下文窗口大小',
          enableGpuAcceleration: '启用GPU加速',
          enableGpuAccelerationHint: '启用GPU加速可以显著提升推理速度，需要CUDA支持',

          // 模型状态
          localModelLoaded: '本地模型已加载',
          localModelNotLoaded: '本地模型未加载',
          loadingModel: '正在加载模型...',
          unloadingModel: '正在卸载模型...',
          detectGpuCapabilities: '检测GPU能力',
          loadModel: '加载模型',
          unloadModel: '卸载模型',
          testLocalModel: '测试本地模型',

          // GPU设备
          gpuDeviceSelection: 'GPU设备选择',
          gpuDeviceInfo: 'GPU设备信息',
          cpuMode: 'CPU模式 (不使用GPU)',
          maxLayers: '最大层数',
          recommendedLayers: '推荐层数',
          memorySize: '内存大小',
          availableMemory: '可用内存',

          // 向量化参数
          vectorizationParams: '向量化参数配置',
          chunkSize: '文档切割大小',
          chunkSizeHint: '单个文档块的最大字符数',
          chunkOverlap: '文档切割重叠',
          chunkOverlapHint: '相邻文档块之间的重叠字符数',
          semanticThreshold: '语义相似度阈值',
          semanticThresholdHint: '语义相似度阈值，用于文档切割时判断语义相关性（0-1之间）',
          searchLimit: '搜索结果限制',
          searchLimitHint: '知识库搜索时返回的最大结果数量',

          // 切割策略
          chunkStrategy: '默认切割策略',
          chunkStrategyHint: '新建知识库时使用的默认文档切割策略',
          chunkStrategyDescription:
            '选择创建新知识库时的默认文档切割策略。不同策略适用于不同类型的文档内容。',

          // 测试
          testApiConnection: '测试API连接',
          testVectorization: '测试向量化',
          debugTool: '调试工具',
          testGpuWorkflow: '测试GPU功能',

          // 错误消息
          selectValidGGUF: '请选择有效的GGUF模型文件',
          gpuLayersNegative: 'GPU层数不能为负数',
          contextSizePositive: '上下文大小必须大于0',
          chunkSizePositive: '文档切割大小必须大于0',
          chunkOverlapNonNegative: '文档切割重叠不能为负数',
          searchLimitPositive: '搜索结果限制必须大于0',

          // 认证错误
          authFailed: '认证失败：请检查 API Key 是否正确',
          serviceNotFound: '服务未找到：请检查 Base URL 是否正确',
          networkError: '网络错误：请检查网络连接和服务器状态',
          requestTimeout: '请求超时：服务器响应时间过长',
          modelError: '模型错误：请检查模型名称是否正确',
          quotaExceeded: '配额不足：请检查账户余额和配额限制',

          // 测试消息
          connectionTestSuccess:
            '连接测试成功！模型: {model}，向量维度: {dimension}，用量: {tokens} tokens',
          vectorizationTestSuccess:
            '向量化测试成功！模型: {model}，向量维度: {dimension}，相似度: {similarity}，用量: {tokens} tokens',
          localModelTestSuccess:
            '本地GGUF模型测试成功！向量维度: {dimension}，推理时间: {time}ms，GPU加速: {gpu}',

          // 状态消息
          gpuDetectionSuccess: 'GPU检测成功！发现 {count} 个GPU设备',
          gpuNotAvailable: 'GPU不可用，将使用CPU进行推理',
          gpuDetectionFailed: 'GPU检测失败: {error}',

          selectedProvider: '已选择：',
          test_gpu_start: '开始测试GPU功能...',
          test_gpu_success: 'GPU功能测试成功！当前使用设备: {device}',
          test_gpu_failed: 'GPU功能测试失败: {error}',
        },

        // Editor Settings
        EditorSettings: {
          title: '编辑器设置',
          fontSize: '字体大小',
          lineHeight: '行高',
          theme: '主题',
          light: '浅色',
          dark: '深色',
          autoSave: '自动保存',
          autoComplete: '自动补全',
          floatAgent: '浮动智能体',
          enableToolbar: '显示工具栏',
          enableEmbeddedTitle: '显示嵌入式标题',
        },

        // Base Settings
        BaseSettings: {
          title: '基础设置',
          interfaceLanguage: '界面语言',
          languageDescription: '更改界面显示语言，设置将自动保存',
          interfaceTheme: '界面主题',
          themeDescription: '更改界面显示主题，设置将自动保存',
          interfaceThemeSaveFailed: '界面主题设置失败',
        },

        // Auto Complete Settings
        AutoCompleteSetting: {
          title: '自动补全设置',
          enableAutoComplete: '启用自动补全',
          triggerMode: '触发模式',
          manual: '手动触发',
          automatic: '自动触发',
          delay: '触发延迟',
          seconds: '秒',
          selectProvider: '选择供应商',
          selectModel: '选择自动补全模型',
          globalPrompt: '全局提示词',
          maxTokens: '最大 Token 数',
          temperature: '温度',
          frequencyPenalty: '频率惩罚',
          presencePenalty: '存在惩罚',
          providerHint: '选择有文本生成、推理模型的LLM供应商',
          modelHint: '选择用于自动补全的模型',
          promptHint:
            '用于自动补全的全局提示词，编辑文档时会自动提供必要提示词，如无必要，此处可不必设置',
          maxTokensHint: '自动补全最大返回Token数量',
          temperatureHint: '值越大越，大模型越具有创造力，反之越严谨',
          frequencyPenaltyHint:
            '根据新 token 在文本中的出现频率对其进行惩罚，从而降低模型逐字重复的可能性',
          presencePenaltyHint:
            '根据新 token 到目前为止是否出现在文本中对其进行惩罚，从而增加模型谈论新主题的可能性',
          needLlmProvider: '需要先启用至少一个LLM供应商才能使用自动补全功能',
          enableHint: '在编辑器中启用智能代码补全功能',
          advancedSettings: '高级设置',
          triggerLength: '触发长度',
          triggerLengthHint: '触发自动补全的最小字符数',
          maxSuggestions: '最大建议数量',
          maxSuggestionsHint: '同时显示的最大建议数量',
          debounceTime: '防抖时间',
          debounceTimeHint: '输入停止后等待多长时间才触发自动补全',
          milliseconds: '毫秒',
          selectedInfo: '已选择：{provider} / {model}',
          apiConfigHint: 'API配置将自动从LLM设置中获取',
        },
        FloatAgentSetting: {
          title: '浮动智能体设置',
          enableFloatAgent: '启用浮动智能体',
          triggerMode: '触发模式',
          manual: '手动触发',
          automatic: '自动触发',
          delay: '触发延迟',
          seconds: '秒',
          selectProvider: '选择供应商',
          selectModel: '选择浮动智能体模型',
          globalPrompt: '全局提示词',
          maxTokens: '最大 Token 数',
          temperature: '温度',
          frequencyPenalty: '频率惩罚',
          presencePenalty: '存在惩罚',
          providerHint: '选择有文本生成、推理模型的LLM供应商',
          modelHint: '选择用于浮动智能体的模型',
          promptHint:
            '用于浮动智能体的全局提示词，编辑文档时会自动提供必要提示词，如无必要，此处可不必设置',
          maxTokensHint: '浮动智能体最大返回Token数量',
          temperatureHint: '值越大越，大模型越具有创造力，反之越严谨',
          frequencyPenaltyHint:
            '根据新 token 在文本中的出现频率对其进行惩罚，从而降低模型逐字重复的可能性',
          presencePenaltyHint:
            '根据新 token 到目前为止是否出现在文本中对其进行惩罚，从而增加模型谈论新主题的可能性',
          needLlmProvider: '需要先启用至少一个LLM供应商才能使用浮动智能体功能',
          enableHint: '在编辑器中启用浮动智能体功能',
          advancedSettings: '高级设置',
          triggerLength: '触发长度',
          triggerLengthHint: '触发浮动智能体操作的最小字符数',
          selectedInfo: '已选择：{provider} / {model}',
          apiConfigHint: 'API配置将自动从LLM设置中获取',
        },

        PromptSettings: {
          promptName: '新提示词名称',
          add: '添加',
          delete: '删除',
          responsibilities: '职责要求',
          emphasize: '强调内容',
          autoCompleteDescription: '根据光标位置和前后文内容，自动进行续写。',
          floatAgentDescription: '对选中文本或在光标位置进行改写或续写。',
          chatDescription: '在对话根据用户要求执行操作。',
          newResponsibility: '新职责要求',
          autoComplete: '自动补全',
          floatAgent: '改写',
          chat: '对话',
        },

        // Search Engine Settings
        SearchEngineSettings: {
          title: '搜索引擎设置',
          description: '配置搜索引擎参数',
          provider: '搜索引擎',
          apiKey: 'API密钥',
          baseUrl: 'API地址',
          selectProvider: '选择搜索引擎供应商',
          selectHint: '在左侧列表中选择要配置的供应商',
        },

        // Resource Provider Settings
        ResourceProviderSettings: {
          title: '资源服务商设置',
          description: '配置资源服务参数',
          provider: '资源服务商',
          apiKey: 'API密钥',
          baseUrl: 'API地址',
          selectProvider: '选择资源服务商',
          selectHint: '在左侧列表中选择要配置的供应商',
        },

        // Model Manager
        ModelManager: {
          title: '模型管理',
          description: '管理本地和云端模型',
          localModels: '本地模型',
          cloudModels: '云端模型',
          refresh: '刷新',
          addModel: '添加模型',
          removeModel: '移除模型',
          modelName: '模型名称',
          modelSize: '模型大小',
          modelPath: '模型路径',
          noModels: '暂无模型，点击右上角"添加模型"开始配置',
          selectModel: '选择',
          inputModel: '输入',
          selectRequired: '请选择模型',
          inputRequired: '请输入模型名称',
          modelExists: '模型已存在',
          toolsCalls: '工具调用',
          newCategory: '新分类',
          removeModelTooltip: '点击 × 删除模型',
        },
        // LLM Provider Settings
        llmProviders: {
          baseUrl: 'API 基础 URL',
          baseUrlRules: {
            required: '请输入 API 基础 URL',
            hint: 'DeepSeek API 服务地址',
          },
          apiKeyRules: {
            required: '请输入 API Key',
            hint: 'API 服务地址',
          },
          apiKey: 'API Key',
          maxTokens: '最大输出 Tokens',
          temperature: '温度',
          topP: 'Top P',
          frequencyPenalty: '频率惩罚',
          presencePenalty: '存在惩罚',
          seed: '随机种子',
          parallelToolCalls: '启用并行工具调用',
          enable: '启用',
          advancedSettings: '高级设置',
          modelManagement: '可用模型管理',
          temperatureDescription:
            '控制模型输出的随机性。较高的值会使输出更加随机，而较低的值会使输出更加集中确定。',
          topPDescription: '核采样的概率阈值。取值越大生成的随机性越高，取值越低生成的确定性越高。',
          frequencyPenaltyDescription:
            '正数会根据到目前为止文本中的现有频率来惩罚新令牌，降低模型逐字重复相同行的可能性。',
          presencePenaltyDescription:
            '正数会根据新令牌是否出现在到目前为止的文本中来惩罚它们，增加模型讨论新主题的可能性。',
          seedDescription: '设置随机种子以获得更确定性的输出（可选）',
          parallelToolCallsDescription: '允许模型在单个响应中并行调用多个工具，提高效率',
          apiKeyHint: '请输入 API Key',
          baseUrlHint: '请输入 API 基础 URL',
          maxTokensHint: '模型输出的最大 token 数量',
          temperatureHint: '控制模型输出的随机性',
          topPHint: '核采样的概率阈值',
          frequencyPenaltyHint: '根据现有频率惩罚重复内容',
          presencePenaltyHint: '根据新令牌出现情况惩罚',
          seedHint: '设置随机种子以获得确定性输出',
          titleSuffix: '的连接参数和模型选项',
          enabled: '启用',

          serviceEndpoint: '服务地址',
          apiKeyRueles: {
            required: '请输入 API Key',
            hint: 'API 服务地址',
          },
          getApiKey: '密钥获取',
          maxTokensRules: {
            required: '请输入有效的 token 数量',
            hint: '模型输出的最大 token 数量（默认 4096）',
          },
          randomnessType: '随机性控制方式',
          thinkTooltip: '允许 Claude 进行更深入的内部推理，提高复杂问题的回答质量',
          thinkInputLabel: '思考预算 (Tokens)',
          thinkInputRules: {
            required: '思考预算至少需要 1024 tokens',
            hint: '内部推理过程可使用的最大 token 数量',
          },
          enableInterleavedThinking: '启用交错思考（Beta）',
          enableInterleavedThinkingTooltip:
            '允许 Claude 在工具调用之间进行思考，提高工具使用的准确性',
          authType: '认证方式',
          authTypeRules: {
            required: '请选择认证方式',
            hint: '选择认证方式（目前仅支持 API Key）',
          },
          parallelToolCallsTooltip: '允许模型在单个响应中并行调用多个工具，提高效率',
          seedRules: {
            required: '请输入随机种子',
            hint: '设置随机种子以获得更确定性的输出（可选）',
          },
          topK: 'Top K',
          topKDescription: '采样时考虑的词元数量。较小的值会使输出更加集中，较大的值会增加多样性。',
          candidateCount: '候选数量',
          candidateCountRules: {
            required: '请输入 1-8 之间的数字',
            hint: '生成的候选响应数量（1-8）',
          },
          stream: '流式输出',
          streamTooltip: '启用流式输出可以实时看到生成的内容',
          stopSequences: '停止序列',
          stopSequencesRules: {
            hint: '输入停止序列，用逗号分隔（可选）',
          },
          testConnection: '测试连接',
          temperatureOptions: {
            label: '温度',
            value: 'temperature',
          },
          topPOptions: {
            label: 'Top P',
            value: 'topP',
          },
          enableParallelToolCalls: '启用并行工具调用',
          frequencyPenaltyValue: '频率惩罚值',
          presencePenaltyValue: '存在惩罚值',
          seedValue: '随机种子值',
          temperatureValue: '温度值',
          topPValue: 'Top P值',

          // Provider specific settings
          qwen: {
            title: '通义千问设置',
            description: '配置通义千问服务的连接参数和模型选项',
            baseUrlHint: '通义千问大模型API服务地址',
            apiKeyGet: '通义千问 API 密钥获取',
          },

          openai: {
            title: 'OpenAI 设置',
            description: '配置 OpenAI API 的连接参数和模型选项',
            baseUrlHint: 'OpenAI API 服务地址',
            apiKeyGet: 'OpenAI API 密钥获取',
          },

          anthropic: {
            title: 'Anthropic 设置',
            description: '配置 Anthropic Claude 服务的连接参数和模型选项',
            baseUrlHint: 'Anthropic API 服务地址',
            apiKeyGet: 'Anthropic API 密钥获取',
            thinkingEnabled: '启用思维链',
            thinkingEnabledDescription: '启用思维链功能，让模型展示其推理过程',
          },

          gemini: {
            title: 'Gemini 设置',
            description: '配置 Google Gemini 服务的连接参数和模型选项',
            baseUrlHint: 'Google AI API 服务地址',
            apiKeyGet: 'Google AI API 密钥获取',
          },

          moonshot: {
            title: '月之暗面设置',
            description: '配置 Moonshot AI 服务的连接参数和模型选项',
            baseUrlHint: 'Moonshot AI API 服务地址',
            apiKeyGet: 'Moonshot AI API 密钥获取',
          },

          deepseek: {
            title: 'DeepSeek 设置',
            description: '配置 DeepSeek 服务的连接参数和模型选项',
            baseUrlHint: 'DeepSeek API 服务地址',
            apiKeyGet: 'DeepSeek API 密钥获取',
          },

          minimax: {
            title: 'MiniMax 设置',
            description: '配置 MiniMax 服务的连接参数和模型选项',
            baseUrlHint: 'MiniMax API 服务地址',
            apiKeyGet: 'MiniMax API 密钥获取',
          },

          ollama: {
            title: 'Ollama 设置',
            description: '配置本地 Ollama 服务的连接参数和模型选项',
            baseUrlHint: 'Ollama 本地服务地址',
            apiKeyGet: 'Ollama 不需要 API Key',
          },

          azureOpenai: {
            title: 'Azure OpenAI 设置',
            description: '配置 Azure OpenAI 服务的连接参数和模型选项',
            baseUrlHint: 'Azure OpenAI 服务地址',
            apiKeyGet: 'Azure OpenAI API 密钥获取',
            deploymentName: '部署名称',
            deploymentNameRules: {
              required: '请输入部署名称',
              hint: '在 Azure Portal 中创建的模型部署名称',
            },
            apiVersion: 'API 版本',
            apiVersionRules: {
              required: '请选择 API 版本',
              hint: '选择要使用的 API 版本',
            },
          },

          volces: {
            title: '火山引擎设置',
            description: '配置火山引擎服务的连接参数和模型选项',
            baseUrlHint: '火山引擎 API 服务地址',
            apiKeyGet: '火山引擎 API 密钥获取',
          },

          grok: {
            title: 'Grok 设置',
            description: '配置 xAI Grok 服务的连接参数和模型选项',
            baseUrlHint: 'xAI API 服务地址',
            apiKeyGet: 'xAI API 密钥获取',
          },

          glm: {
            title: '智谱 GLM 设置',
            description: '配置智谱 GLM 服务的连接参数和模型选项',
            baseUrlHint: '智谱 GLM API 服务地址',
            apiKeyGet: '智谱 GLM API 密钥获取',
          },
        },

        // Resource Provider Settings
        resourceProviders: {
          apiKey: 'API Key',
          baseUrl: 'Base URL',
          maxResults: '最大结果数',
          searchDepth: '搜索深度',
          includeAnswer: '包含AI答案',
          includeRawContent: '包含原始内容',
          includeImages: '包含图片',
          includeImageDescriptions: '包含图片描述',
          apiKeyPlaceholder: '请输入你的 API Key',
          apiKeyHint: '用于访问 API',
          baseUrlPlaceholder: '请输入 API 基础地址',
          baseUrlHint: 'API 基础地址',
          maxResultsPlaceholder: '请输入最大结果数',
          maxResultsHint: '设置搜索返回的最大结果数量',
          searchOptions: '搜索选项',
          includeAnswerHint: '是否在搜索结果中包含AI生成的答案摘要',
          includeRawContentHint: '是否在搜索结果中包含网页的原始内容',
          includeImagesHint: '是否在搜索结果中包含相关图片',
          includeImageDescriptionsHint: '是否为图片生成描述文本（需要先启用包含图片）',

          // Provider specific
          pexels: {
            title: 'Pexels 设置',
            description: '配置 Pexels 图片服务',
          },

          tavily: {
            title: 'Tavily 搜索设置',
            description: '配置 Tavily 搜索引擎',
            searchDepthOptions: {
              basic: '基础搜索',
              advanced: '高级搜索',
            },
          },
        },

        knowledgeSearch: '知识库',
        pexelsIntegration: '媒体库',

        // Search & Replace
        searchReplace: {
          title: '查找与替换',
          search: '查找',
          replace: '替换',
          replaceAll: '全部替换',
          caseSensitive: '区分大小写',
          wholeWord: '全词匹配',
          useRegex: '使用正则表达式',
          noResults: '未找到匹配内容',
          results: '找到 {count} 个匹配项',
          confirmReplaceAll: '确认替换所有匹配项？',
          replaceSuccess: '已成功替换 {count} 个匹配项',
          searchPlaceholder: '查找内容...',
          replacePlaceholder: '替换为...',
          clearSearch: '清空搜索',
          next: '下一个',
          previous: '上一个',
        },

        // Common actions
        common: {
          save: '保存',
          reset: '重置',
          cancel: '取消',
          confirm: '确认',
          delete: '删除',
          edit: '编辑',
          add: '添加',
          test: '测试',
          enable: '启用',
          disable: '禁用',
          enabled: '已启用',
          disabled: '已禁用',
          times: '倍',
          settings: '设置',
        },

        providers: {
          PexelsProviderConfig: {
            apiKeyLabel: 'Pexels API Key',
            apiKeyPlaceholder: '请输入 Pexels API Key',
            apiKeyHint: 'Pexels API Key',
            apiKeyRules: {
              required: '请输入 Pexels API Key',
            },
            baseUrlLabel: 'Base URL',
            baseUrlPlaceholder: 'https://api.pexels.com',
            baseUrlHint: 'Pexels API 基础地址',
            maxResultsLabel: '最大结果数',
            maxResultsPlaceholder: '请输入最大结果数',
            maxResultsHint: '设置搜索返回的最大图片数量',
          },
          TavilyProviderConfig: {
            apiKeyLabel: 'Tavily API Key',
            apiKeyPlaceholder: '请输入 Tavily API Key',
            apiKeyHint: 'Tavily API Key',
            apiKeyRules: {
              required: '请输入 Tavily API Key',
            },
            baseUrlLabel: 'Base URL',
            baseUrlPlaceholder: 'https://api.tavily.com/search',
            baseUrlHint: 'Tavily API 基础地址',
            searchDepthLabel: '搜索深度',
            searchOptions: '搜索选项',
            searchDepthOptions: {
              basic: '基础搜索',
              advanced: '高级搜索',
            },
            searchDepthHint: '选择搜索的深度级别',
            maxResultsLabel: '最大结果数',
            maxResultsPlaceholder: '请输入最大结果数',
            maxResultsHint: '设置搜索返回的最大结果数量',
            includeAnswerLabel: '包含AI答案',
            includeAnswerHint: '是否在搜索结果中包含AI生成的答案摘要',
            includeRawContentLabel: '包含原始内容',
            includeImagesLabel: '包含图片',
            includeImageDescriptionsLabel: '包含图片描述',
            includeRawContentHint: '是否在搜索结果中包含网页的原始内容',
            includeImagesHint: '是否在搜索结果中包含相关图片',
            includeImageDescriptionsHint: '是否为图片生成描述文本（需要先启用包含图片）',
          },
        },
      },
      pages: {
        KnowledgeBasePage: {
          notConfigured: {
            title: '知识库未配置',
            description:
              '请先在设置中配置知识库参数（API URL、密钥和模型），并通过连接测试后才能使用知识库功能。',
            buttonLabel: '前往配置',
          },
        },
      },
      AddDocumentDialog: {
        title: '从文档库添加文档',
        searchPlaceholder: '搜索文档标题...',
        selectedCount: '已选 {count} 个',
        loading: '加载文档列表中...',
        noMatch: '未找到匹配的文档',
        noDocuments: '文档库中暂无可添加的文档',
        unnamedDocument: '未命名文档',
        addSelectedDocuments: '添加选中文档',
        tryOtherKeywords: '请尝试其他搜索关键词',
        allDocumentsAdded: '所有文档可能已添加到当前知识库',
        totalDocuments: '共 {count} 个文档',
      },
      AssistantMessage: {
        thinking: '思考中...',
        thinkingProcess: '思考过程',
        toolCall: 'AI工具调用',
        saveToKnowledge: '保存到知识库',
        saveToKnowledgeHint: '请先在设置中配置知识库参数',
        aiAnswer: 'AI回答',
      },
      ChatConversationList: {
        history: '历史对话',
        showLess: '收起',
        showAll: '查看全部',
        rename: '重命名',
        delete: '删除',
        renameDialog: '重命名对话',
        conversationTitle: '对话标题',
        cancel: '取消',
        confirm: '确认',
        loadFailed: '加载对话列表失败',
        createFailed: '创建对话失败',
        renameSuccess: '对话重命名成功',
        renameFailed: '重命名失败',
        confirmDelete: '确认删除',
        confirmDeleteMessage: '确定要删除对话"{title}"吗？此操作不可撤销。',
        cancelDelete: '用户取消',
        deleteSuccess: '对话删除成功',
        deleteFailed: '删除对话失败',
      },
      ChunkingStrategySelector: {
        title: '切割策略配置',
        description: '选择合适的切割策略来处理文档内容，不同策略适用于不同类型的文档',
        methodLabel: '切割方法',
        methodOptions: {
          markdown: 'Markdown',
          recursiveCharacter: '递归字符',
        },
        comingSoon: '即将支持',
        configuration: '配置参数',
        chunkSize: '切割大小',
        chunkOverlap: '重叠大小',
        characters: '字符',
        chunkSizeRule1: '必须大于0',
        chunkSizeRule2: '不能超过5000字符',
        chunkOverlapRule1: '不能小于0',
        chunkOverlapRule2: '重叠不能超过切割大小',
        markdownSuggestion: '建议：Markdown 文档使用 600-1000 字符，保持标题结构完整',
        latexSuggestion: '建议：LaTeX 文档使用 800-1200 字符，保持公式和环境完整',
        recursiveCharacterSuggestion: '建议：普通文本使用 500-800 字符，在自然边界处分割',
        smartSuggestion: '建议：智能切割会自动选择最适合的策略，使用 600-1000 字符',
        defaultSuggestion: '建议：一般文档使用 500-800 字符，技术文档可使用 800-1200 字符',
        strategyInfo: '策略详细信息',
        useCases: '适用场景',
        mainAdvantages: '主要优势',
        usageRestrictions: '使用限制',
        loadFailed: '加载切割方法失败',
        markdown: 'Markdown',
        markdownDescription: '专门针对 Markdown 文档的结构化切割',
        configFailed: '配置验证失败',
        configValid: '配置有效',
        configInvalid: '配置参数有误: {errors}',
        recursiveCharacter: '递归字符',
        recursiveCharacterDescription: '递归字符切割器',
      },
      ConversitionHistory: {
        addConversation: '新建对话',
        deleteConversation: '删除对话',
      },
      CreateDocument: {
        title: '文档名称',
      },
      CreateDocumentDialog: {
        title: '创建文档',
        rule: '请输入文档标题',
        processing: '处理中...',
        save: '保存',
        cancel: '取消',
        document_config: '文档配置',
        chunking_progress: '切割进度',
        chunking_result: '切割结果',
        chunking_count: '切割块数',
        average_chunk_size: '平均块大小',
        chunking_size_range: '大小范围',
        characters: '字符',
        chunking_completed: '文档切割完成！共 {count} 个块，平均大小 {averageSize} 字符',
        chunking_failed: '文档切割失败: {error}',
        document_creating: '文档创建中，切割任务已提交到后台处理...',
        submit_chunking_failed: '提交切割任务失败: {error}',
        unsaved_changes: '您有未保存的更改，确定要关闭吗？',
        confirm_close: '确认关闭',
        please_input_title: '请输入文档标题',
        please_input_content: '请输入文档内容',
        please_check_chunking_config: '请检查切割策略配置',
      },
      CreateFolder: {
        title: '文件夹名称',
      },
      CreatekKnowlegeDocCard: {
        save_to_knowledge_base: '保存到知识库',
        select_knowledge_base_and_set_title: '选择知识库并设置文档标题',
        document_title: '文档标题',
        select_knowledge_base: '选择知识库',
        no_knowledge_base: '暂无知识库，请先创建知识库',
        create_new_knowledge_base: '创建新知识库',
        content_preview: '内容预览',
        save: '保存',
        please_input_title: '请输入文档标题',
        please_input_knowledge_base_name: '请输入知识库名称',
        knowledge_base_name: '知识库名称',
        knowledge_base_description: '知识库描述',
        create: '创建',
        cancel: '取消',
        already_added: '已添加到该知识库',
      },
      DocManager: {
        newFolder: '新建文件夹',
        customSort: '自定义排序',
        alphabeticalSort: '按名称排序',
      },
      DragDropIndicator: {
        dropHere: '放置到此处',
      },
      DocumentItem: {
        add_to_conversation: '添加到对话',
        add_to_knowledge_base: '添加到知识库',
        quick_add_to_knowledge_base: '快速添加到知识库',
        no_knowledge_bases_available: '暂无可用知识库',
        please_create_knowledge_base_first: '请先创建知识库',
        documents_count: '{count} 个文档',
        rename: '重命名',
        copy: '复制',
        cut: '剪切',
        delete: '删除',
        document_not_in_knowledge_base: '文档未添加到知识库',
        knowledge_document_not_found: '未找到对应的知识库文档',
        already_added: '已添加',
        document_already_in_knowledge_base: '文档已添加到知识库"{name}"，不能重复添加',
      },
      FolderItem: {
        new_subfolder: '新建子文件夹',
        new_document: '新建文档',
        rename: '重命名',
        paste: '粘贴',
        add_all_to_knowledge_base: '批量添加到知识库',
        delete: '删除',
        creating_content_cannot_fold: '正在创建内容，无法折叠',
        no_document_to_paste: '没有可粘贴的文档',
        document_moved_to_folder: '文档"{title}"已移动到文件夹',
        document_copied_to_folder: '文档"{title}"已复制到文件夹',
        paste_document_failed: '粘贴文档失败',
        confirm_delete_folder_title: '确认删除文件夹',
        confirm_delete_folder_message: '确定要删除文件夹"{name}"吗？此操作不可撤销。',
        folder_deleted_success: '文件夹"{name}"已成功删除',
        folder_deleted_failed: '删除文件夹"{name}"失败: {error}',
      },
      BatchKnowledgeBaseConfigCard: {
        batch_add_to_knowledge_base: '批量添加到知识库',
        select_knowledge_base_and_chunking_strategy: '选择知识库和切割策略',
        select_knowledge_base: '选择知识库',
        please_select_knowledge_base: '请选择知识库',
        no_knowledge_base: '暂无知识库',
        create_new_knowledge_base: '创建新知识库',
        chunking_strategy: '切割策略',
        chunking_method: '切割方法',
        recursive_character_text_splitter: '递归字符文本分割器',
        markdown_text_splitter: 'Markdown文本分割器',
        latex_text_splitter: 'LaTeX文本分割器',
        smart_text_splitter: '智能文本分割器',
        chunk_size: '块大小',
        chunk_overlap: '块重叠',
        cancel: '取消',
        start_batch_processing: '开始批量处理',
      },
      KnowledgeBaseDetail: {
        upload_document: '上传文档',
        create_document: '创建文档',
        add_document_from_document_library: '从文档库添加文档',
        loading: '加载中...',
        no_description: '暂无描述',
        created_time: '创建时间',
        documents: '文档',
        knowledge_chunks: '片段',
        knowledge_base_documents: '知识库文档',
        no_documents: '暂无文档',
        click_add_document: '点击添加文档',
        type: '类型',
        editor_document: '编辑文档',
        processing_vectorization: '处理向量化中...',
        add_time: '添加时间',
        chunk_count: '片段数量',
        load_failed: '加载失败',
        cannot_load_knowledge_base_information: '无法加载知识库信息',
        retry: '重试',
        upload_document_to_knowledge_base: '上传文档到知识库',
        select_document_file: '选择文档文件',
        document_title: '文档标题',
        please_input_document_title: '请输入文档标题',
        document_description: '文档描述',
        cancel: '取消',
        upload: '上传',
        save: '保存',
        document_configuration: '文档配置',
        add_document_to_knowledge_base: '添加文档到知识库',
        search_document: '搜索文档',
        update_time: '更新时间',
        add_selected_documents: '添加选中文档',
        knowledge_base_settings: '知识库设置',
        knowledge_base_name: '知识库名称',
        knowledge_base_description: '知识库描述',
        settings: '设置',
        close_editor: '关闭编辑器',
        document_not_found: '找不到指定的文档',
        document_id: '文档ID',
        document_may_be_deleted_or_data_synchronization_exception: '可能文档已被删除或数据同步异常',
        document_type: '文档类型',
        unknown: '未知',
        not_support_online_editing: '暂不支持在线编辑',
        only_support_markdown_and_text_type_document_editing:
          '仅支持 Markdown 和文本类型的文档编辑',
        open_document_failed: '',
        unknown_error: '请输入文档内容',
        confirm_close: '文档保存成功，向量已更新',
        document_has_been_modified_but_not_saved: '文档已修改但未保存，确定要关闭吗？',
        please_input_document_content: '请输入文档内容',
        document_saved_successfully: '文档保存成功，向量已更新',
        save_document_failed: '保存文档失败',
        document_content_is_empty: '内容为空',
        skip_adding: '跳过添加',
        unnamed_document: '未命名文档',
        add_document_failed: '添加文档失败',
        duplicate_document: '已存在同名文档',
        already_exists: '已存在',
        successfully_added: '成功添加',
        documents_to_knowledge_base: '个文档到知识库',
        skipped: '跳过',
        duplicate_documents: '个重复文档',
        documents_failed_to_add: '个文档添加失败',
        all_selected_documents_already_exist_in_the_knowledge_base: '所选文档均已存在于知识库中',
        confirm_remove: '确认移除',
        are_you_sure_you_want_to_remove_the_document: '确定移除：',
        remove_document_failed: '移除文档失败',
        please_input_knowledge_base_name: '请输入知识库名称',
        settings_format_is_incorrect: '设置格式不正确，请输入有效的JSON',
        settings_saved_successfully: '设置保存成功',
        save_settings_failed: '保存设置失败',
        database_is_not_initialized_yet: '数据库尚未初始化完成，请稍后再试',
        document_upload_successfully: '文档上传成功',
        upload_document_failed: '上传文档失败，请重试',
        knowledge_base_function_is_temporarily_unavailable: '知识库功能暂时不可用，请重启应用程序',
        file_read_failed: '文件读取失败',
        document_created_successfully: '文档创建成功',
        create_document_failed: '创建文档失败',
        document_vectorized_successfully: '文档向量化完成',
        generated: '生成了',
        knowledge_fragments: '个知识片段',
        document_vectorization_failed: '文档向量化失败',
        data_refresh_completed: '数据刷新完成',
        data_refresh_failed: '数据刷新失败',
        database_is_not_initialized: '数据库尚未初始化',
        using_default_chunking_strategy: '使用默认切割策略',
        fallback_create_failed: '回退创建也失败',
        chunking_failed: '文档切割失败',
      },
      KnowledgeBaseDetailView: {
        upload_document: '上传文档',
        create_document: '创建文档',
        add_document_from_document_library: '从文档库添加文档',
        add_document_from_document_library_tip: '请直接在 文件 / 文件夹 上右键，选择“添加到知识库”',
        refresh: '刷新',
        settings: '设置',
        loading: '加载中...',
        documents: '文档',
        no_description: '暂无描述',
        created_time: '创建时间',
        no_documents: '暂无文档',
        click_add_document: '点击添加文档',
        type: '类型',
        editor_document: '编辑文档',
        processing_vectorization: '处理向量化中...',
        add_time: '添加时间',
        chunk_count: '片段数量',
        knowledge_chunks: '片段',
        knowledge_base_documents: '知识库文档',
        vectorization_completed: '向量化完成',
        delete_document: '删除文档',
        cannot_load_knowledge_base_information: '无法加载知识库信息',
        retry: '重试',
        load_failed: '加载失败',
        revectorization: '重新向量化',
        need_revectorization: '需要重新向量化',
        revectorization_knowledge_base: '知识库重新向量化',
        revectorize_confirm:
          '确定要重新向量化文档"{title}"吗？这将删除现有的所有片段和向量，使用当前知识库配置重新切片并向量化。',
        revectorize_started: '开始重新向量化文档"{title}"...',
        chunking_completed: '文档"{title}"切割完成，生成 {chunks} 个文本块',
        revectorize_error: '重新向量化失败: {error}',
        remove: '移除',
        revectorize_knowledge_base_confirm:
          '确定要重新向量化整个知识库 "{name}" 吗？这将删除现有的所有片段和向量，使用当前知识库配置重新切片并向量化。共 {count} 个文档。',
        ignoreRevectorize: '忽略重新向量化',
        ignoreRevectorizeConfirm: {
          title: '忽略重新向量化',
          message:
            '确定要忽略文档"{title}"的重新向量化需求吗？这将更新知识库文档的时间戳，使系统认为文档是最新的。',
        },
        ignoreRevectorizeSuccess: '已忽略文档"{title}"的重新向量化需求',
        ignoreRevectorizeError: '忽略重新向量化失败: {error}',
      },
      KnowledgeBaseList: {
        rag_knowledge_base_management: 'RAG 知识库管理',
        loading: '加载中...',
        no_knowledge_base: '暂无知识库',
        click_create_knowledge_base: '点击"新建知识库"开始创建您的第一个知识库',
        document_count: '文档数量',
        created_time: '创建时间',
        edit: '编辑',
        delete: '删除',
        back: '返回',
      },
      KnowledgeBaseManager: {
        document_title: '文档标题',
        please_input_document_title: '请输入文档标题',
        save: '保存',
        edit_original_document: '编辑原始文档',
        document_created_successfully: '文档创建成功',
        close_editor: '关闭编辑器',
        confirm_delete: '确认删除',
        confirm_delete_message: '确定要删除知识库"{name}"吗？此操作不可恢复。',
        database_is_not_initialized_yet: '数据库尚未初始化完成，请稍后再试',
        create_document_failed: '创建文档失败',
        create_document_successfully: '创建文档成功',
        document_upload_successfully: '文档上传成功',
        chunking_failed: '文档切割失败',
        unknown_error: '未知错误',
        successfully_added: '成功添加',
        documents_to_knowledge_base: '个文档到知识库',
        skipped: '跳过',
        duplicate_documents: '个重复文档',
        documents_failed_to_add: '个文档添加失败',
        all_selected_documents_already_exist_in_the_knowledge_base: '所选文档均已存在于知识库中',
        confirm_remove: '确认移除',
        chunking_result_submitted: '切割结果已提交！共 {chunkCount} 个块，开始向量化处理...',
        submit_chunking_result_failed: '提交切割结果失败',
        unnamed_document: '未命名文档',
        document_not_found: '找不到指定的文档',
        document_type: '文档类型',
        unknown: '未知',
        not_support_online_editing: '暂不支持在线编辑',
        open_document_failed: '打开文档失败',
        confirm_close: '确认关闭',
        document_has_been_modified_but_not_saved: '文档已修改但未保存，确定要关闭吗？',
        document_saved_successfully: '文档保存成功，向量已更新',
        save_document_failed: '保存文档失败',
        add_document_failed: '添加失败',
        confirm_remove_message: '确定要从知识库中移除文档"{name}"吗？',
        document_vectorized_successfully: '文档向量化完成',
        document_vectorization_failed: '文档向量化失败',
        document_id: '文档ID',
        generated: '生成了',
        knowledge_fragments: '个知识片段',
      },
      KnowledgeBaseSettingsDialog: {
        edit_knowledge_base: '编辑知识库',
        create_knowledge_base: '创建知识库',
        knowledge_base_name: '知识库名称',
        please_input_knowledge_base_name: '请输入知识库名称',
        description: '描述',
        optional_description_hint: '可选，描述知识库的用途或内容',
        knowledge_base_name_at_least_2_characters: '知识库名称至少需要2个字符',
        knowledge_base_name_cannot_exceed_100_characters: '知识库名称不能超过100个字符',
        advanced_settings: '高级设置',
        visual_editing: '可视化编辑',
        json_editing: 'JSON 编辑',
        chunking_strategy: '分块策略',
        chunking_strategy_hint: '文档分块的默认策略',
        chunk_size: '分块大小',
        chunk_size_hint: '每个文档块的最大字符数',
        chunk_overlap: '分块重叠',
        chunk_overlap_hint: '相邻块之间的重叠字符数',
        similarity_threshold: '相似度阈值',
        similarity_threshold_hint: '搜索时的最小相似度阈值 (0-1)',
        max_search_results: '最大检索结果数',
        max_search_results_hint: '每次搜索返回的最大结果数',
        settings_json_format: '设置 (JSON 格式)',
        settings_json_format_hint: 'JSON 格式的知识库设置，留空使用默认设置',
        format_json: '格式化 JSON',
        reset_to_default: '重置为默认',
        confirm: '确认',
        cancel: '取消',
        please_input_valid_json_format: '请输入有效的 JSON 格式',
        json_format_success: 'JSON 格式化成功',
        invalid_json_format: '无效的 JSON 格式，无法格式化',
        reset_to_default_success: '已重置为默认设置',
        please_check_form_errors: '请检查表单中的错误项',
        save_failed: '保存失败，请重试',
      },
      KnowledgeDebug: {
        knowledge_base_debug_tool: '知识库调试工具',
        database_status_check: '数据库状态检查',
        check_database_status: '检查数据库状态',
        check_chunk_data: '检查知识块数据',
        test_embedding: '测试向量生成',
        test_text: '测试文本',
        test_api_connection: '测试API连接',
        test_local_model: '测试本地模型',
        embedding_result: '向量化结果',
        search_query: '搜索查询',
        select_knowledge_base: '选择知识库',
        debug_search_pipeline: '调试搜索流程',
        test_search: '测试搜索',
        search_result: '搜索结果',
        document_id: '文档ID',
        data_repair_tool: '数据修复工具',
        database_status: '数据库状态',
        start_checking_database_status: '开始检查数据库状态...',
        database_status_check_completed: '数据库状态检查完成',
        database_status_check_failed: '数据库状态检查失败：',
        unknown_error: '未知错误',
        start_checking_chunk_data: '开始检查知识块数据...',
        chunk_data_check_completed: '知识块数据检查完成',
        chunk_data_check_failed: '知识块数据检查失败：',
        start_testing_embedding: '开始测试向量生成：',
        embedding_generation_completed: '向量生成完成',
        embedding_generation_failed: '向量生成失败：',
        start_testing_api_connection: '开始测试API连接...',
        api_connection_test_success: 'API连接测试成功',
        api_connection_test_failed: 'API连接测试失败：',
        start_testing_local_model: '开始测试本地模型...',
        local_model_test_completed: '本地模型测试完成',
        local_model_test_failed: '本地模型测试失败：',
        start_testing_search: '开始测试搜索：',
        search_completed: '搜索完成，结果数：',
        search_failed: '搜索失败：',
        start_debugging_search_pipeline: '开始调试搜索流程...',
        search_pipeline_debug_completed: '搜索流程调试完成',
        search_pipeline_debug_failed: '搜索流程调试失败：',
        start_regenerating_document_chunks: '开始重新生成文档块：',
        document_chunks_regenerated: '文档块已重新生成：',
        document_chunks_regenerated_completed: '文档块重新生成完成',
        document_chunks_regenerated_failed: '文档块重新生成失败：',
        start_updating_document_embeddings: '开始更新文档向量：',
        document_embeddings_updated: '文档向量已更新：',
        document_embeddings_updated_completed: '文档向量更新完成',
        document_embeddings_updated_failed: '文档向量更新失败：',
        start_viewing_document_chunks: '开始查看文档块：',
        document_chunks_viewed_completed: '文档块查看完成',
        document_chunks_viewed_failed: '文档块查看失败：',
        repair_result: '修复结果',
        regenerate_chunks: '重新生成知识块',
        update_embeddings: '更新向量',
        view_document_chunks: '查看文档块',
        debug_logs: '调试日志',
        clear_logs: '清空日志',
        logs_cleared: '日志已清空',
        debug_tool_initialized: '调试工具已初始化',
      },
      KnowledgeSearch: {
        placeholder: '搜索知识库内容...',
        search: '搜索',
        scope: '搜索范围',
        result_limit: '结果数量',
        min_score: '最低相似度',
        min_score_hint: '建议0.01-0.1',
        found_results: '找到 {count} 个相关结果',
        query: '查询',
        no_result: '未找到相关内容',
        no_result_tips:
          '尝试以下方法：<br />• 使用不同的关键词<br />• 降低相似度阈值至0.01-0.05<br />• 检查文档是否已完成向量化<br />• 访问 /debug 页面进行诊断',
        retry_lower_threshold: '降低阈值重试',
        debug_diagnose: '调试诊断',
        unnamed_document: '未命名文档',
        best: '最佳',
        better: '较好',
        good: '一般',
        bad: '较差',
        weak: '微弱',
        knowledge_base: '知识库',
        unknown: '未知',
        similarity: '相似度',
        full_document: '完整文档',
        loading_full_document: '加载完整文档中...',
        notify_success: '搜索完成，找到 {count} 个相关结果',
        notify_failed: '搜索失败',
        notify_doc_failed: '获取完整文档内容失败，显示片段内容',
        notify_success_caption: '文档ID: {docId}，生成了 {chunkCount} 个知识片段',
        notify_failed_caption: '文档ID: {docId}',
        notify_success_message: '文档向量化完成',
        notify_failed_message: '文档向量化失败',
      },
      PexelsIntegration: {
        browser: '媒体浏览器',
        selected: '已选择的媒体',
        no_media_selected: '未选择任何媒体',
        select_media_in_browser: '在媒体浏览器中选择图片或视频',
        clear_all: '清空所有',
        export_selection: '导出选择',
        photo: '图片',
        video: '视频 {duration}',
        add_media_to_selection: '已添加{media}到选择列表',
        media_already_in_selection: '该媒体已在选择列表中',
        remove_media_from_selection: '已从选择列表中移除',
        clear_all_selection: '已清空所有选择',
        export_selection_success: '已导出 {count} 个媒体项目',
      },
      PexelsMediaBrowser: {
        search_placeholder: '搜索图片或视频...',
        search: '搜索',
        searching: '正在搜索...',
        search_failed: '搜索失败',
        load_failed: '加载失败',
        retry: '重试',
        try_different_keywords: '尝试使用不同的关键词搜索',
        please_input_search_keywords: '请输入搜索关键词',
        search_high_quality_media: '搜索高质量媒体内容',
        input_keywords_search_pexels: '输入关键词搜索来自 Pexels 的免费图片和视频',
        no_results: '未找到结果',
        pagination_info: '显示第 {start} - {end} 项， 共 {total} 项结果',
        pexels_resource_provider_not_configured: '未配置Pexels资源服务商，请在设置中配置',
        pexels_api_config_incomplete: 'Pexels API 配置不完整，请检查设置',
      },
      SimpleEditor: {
        placeholder: '开始输入内容... 支持Markdown语法',
        unnamed_document: '未命名文档',
        get_title_failed: '获取标题失败',
        get_markdown_failed: '获取Markdown失败',
        title: '标题 {level}',
        body: '正文',
        paragraph: '正文',
      },
      ToolMessage: {
        operation: '操作',
        message: '消息',
        match_info: '匹配信息',
        position: '位置',
        search: '搜索',
        new_content: '新内容',
        view_full_result: '查看完整结果',
        original_text: '原文',
        parse_error: '解析错误',
        unknown: '未知',
      },
      UploadDocumentDialog: {
        upload_document: '上传文档',
        file_upload: '文件上传',
        drag_file_here: '拖拽文件到这里或点击上传',
        supported_formats: '支持 TXT、MD、DOCX、PDF、HTML、JSON、XML、CSV、RTF 格式',
        reselect: '重新选择',
        document_info: '文档信息',
        document_title: '文档标题',
        please_input_document_title: '请输入文档标题',
        document_description: '文档描述（可选）',
        upload: '上传文档',
        cancel: '取消',
        file_validation_passed: '文件验证通过',
        file_size_too_large: '文件大小不能超过 50MB',
        unsupported_file_format:
          '不支持的文件格式，请上传 TXT、MD、DOCX、PDF、HTML、JSON、XML、CSV、RTF 格式的文件',
        please_select_file: '请选择要上传的文件',
        submit_failed: '提交失败，请重试',
      },
    },
    config: {
      llmProviders: {
        description: {
          anthropic: 'Claude 大语言模型',
          azureOpenai: 'Azure OpenAI 服务',
          deepseek: 'DeepSeek 大语言模型',
          gemini: 'Google Gemini AI 模型',
          glm: '智谱AI大语言模型',
          grok: 'xAI Grok 大语言模型',
          minimax: 'MiniMax 大语言模型',
          moonshot: '月之暗面大语言模型',
          ollama: '本地大语言模型服务',
          openai: 'OpenAI GPT 系列模型',
          qwen: '通义千问大语言模型',
          volces: '火山引擎豆包大模型',
        },
        name: {
          anthropic: 'Anthropic',
          azureOpenai: 'Azure OpenAI',
          deepseek: 'DeepSeek',
          gemini: 'Google Gemini',
          glm: '智谱 AI',
          grok: 'xAI Grok',
          minimax: 'MiniMax',
          moonshot: '月之暗面',
          ollama: 'Ollama',
          openai: 'OpenAI',
          qwen: '通义千问',
          volces: '火山引擎豆包',
        },
      },
      resourceMap: {
        description: {
          qwen: '通义千问 -阿里巴巴开发，开源大模型',
          ollama: 'Ollama -本地大模型服务，支持多模型',
          minimax: 'MiniMax - 海螺AI，支持多模型',
          deepseek: 'DeepSeek - 深度学习模型，支持多模型',
          volces: '火山引擎豆包 - 火山引擎豆包大模型',
          moonshot: 'Moonshot - 月之暗面大语言模型',
          anthropic: 'Anthropic - Claude 大语言模型',
          openai: 'OpenAI - GPT 系列大语言模型',
          azureOpenai: 'Azure OpenAI - 微软 Azure 云上的 OpenAI 服务',
          gemini: 'Google Gemini - Google AI 大语言模型',
          grok: 'Grok - xAI 大语言模型',
          glm: '智谱GLM - 智谱AI大语言模型',
          pexels: 'Pexels - 免费图片库',
          tavily: 'Tavily - 网络搜索',
        },
        name: {
          qwen: '通义千问',
          ollama: 'Ollama',
          minimax: 'MiniMax',
          deepseek: 'DeepSeek',
          volces: '火山引擎豆包',
          moonshot: '月之暗面',
          anthropic: 'Anthropic',
          openai: 'OpenAI',
          azureOpenai: 'Azure OpenAI',
          gemini: 'Google Gemini',
          grok: 'Grok',
          glm: '智谱 AI',
          pexels: 'Pexels',
          tavily: 'Tavily',
        },
      },
    },
    layout: {
      MainLayout: {
        pleaseUseDesktop: '请在桌面应用中使用',
      },
    },
    utils: {
      clipboard: {
        successMessage: '复制成功',
        errorMessage: '复制失败',
        checkBrowserPermission: '请检查浏览器权限',
        browserNotSupported: '浏览器不支持剪贴板读取功能',
      },
      editorFactory: {
        placeholder: '开始输入内容...',
      },
      imageManager: {
        saveImageFailed: '图片保存失败',
        getImageFailed: '获取图片失败',
        getImageReferencesFailed: '获取图片引用失败',
        processImageFileFailed: '处理图片文件失败',
        processImageUrlFailed: '处理网络图片失败',
        downloadImageFailed: '下载图片失败',
        triggerImageAssociation: '触发图片关联: {imageId} -> 文档 {documentId}',
        addImageReferenceMethodUnavailable: 'addImageReference 方法不可用',
        triggerImageAssociationFailed: '触发图片关联失败: {error}',
        imageAssociationEventTriggered: '图片关联事件已触发: {imgId} -> {docId}',
        deleteUnreferencedImagesFailed: '删除无引用图片失败: {error}',
      },
      knowledgeBase: {
        markdown: {
          displayName: 'Markdown 切割器',
          description:
            '专门针对 Markdown 文档的结构化切割，能够识别标题、代码块、列表等 Markdown 元素',
          useCases: '技术文档,README 文件,博客文章,项目文档,API 文档,用户手册',
          advantages:
            '保持 Markdown 结构完整性,识别标题层级关系,正确处理代码块,保持列表和表格完整,优化的语义分割',
          limitations: '仅适用于 Markdown 格式,对纯文本效果不佳,需要规范的 Markdown 语法',
          recommendedFor: '包含标题结构的文档,技术说明文档,格式化的文本内容,需要保持结构的文档',
        },

        recursiveCharacter: {
          displayName: '递归字符切割器',
          description: '通用的文本切割器，在自然边界处分割（段落、句子、单词），适用于各种文本类型',
          useCases: '普通文本文档,小说和文学作品,新闻文章,邮件内容,聊天记录,混合格式内容',
          advantages:
            '通用性强，适用于多种文本,智能边界识别,保持语义完整性,递归分割策略,处理中英文混合文本',
          limitations: '不识别特定格式结构,对专业文档格式支持有限,可能分割专业术语',
          recommendedFor: '无特定格式的文本,自然语言内容,对话和交流内容,通用文档处理',
        },

        latex: {
          displayName: 'LaTeX 切割器',
          description: '专门针对 LaTeX 文档的切割器，保持数学公式、环境和命令的完整性',
          useCases: '学术论文,数学文档,科学报告,技术论文,教学材料,研究文档',
          advantages:
            '保持数学公式完整,识别 LaTeX 环境,处理复杂的 LaTeX 命令,保持定理证明结构,适合学术内容',
          limitations: '仅适用于 LaTeX 格式,需要正确的 LaTeX 语法,对其他格式无效',
          recommendedFor: '包含数学公式的文档,学术和科研内容,LaTeX 源文件,技术论文',
        },

        smart: {
          displayName: '智能切割器',
          description:
            '自动检测内容类型并选择最合适的切割策略，支持 LaTeX、Markdown 和普通文本的自动识别',
          useCases: '混合格式文档,未知格式内容,批量文档处理,自动化工作流,多格式知识库,通用文档处理',
          advantages: '自动格式检测,无需手动选择策略,支持多种格式,智能策略选择,简化使用流程',
          limitations: '检测可能不够精确,复杂格式可能误判,依赖内容特征识别',
          recommendedFor: '格式不确定的文档,自动化处理场景,多样化内容处理,快速原型开发',
        },
      },
    },
    composeables: {
      useAnthropic: {
        toolExecutionFailed: '工具执行失败: {error}',
        errorOccurred: '抱歉，发生了错误，请稍后重试。',
      },
      useAzureOpenAI: {
        toolExecutionFailed: '工具执行失败: {error}',
        errorOccurred: '抱歉，发生了错误，请稍后重试。',
      },
      useConversition: {
        selectModel: '选择模型',
        agent: 'Agent',
        agentDescription: '执行复杂任务的智能体',
        copilot: 'Copilot',
        copilotDescription: '拥有修改文档内容、名称等能力',
        chat: 'Chat',
        chatDescription: '可以读取内容信息，但不会执行修改操作',
      },
      useDeepSeek: {
        toolExecutionFailed: '工具执行失败: {error}',
        errorOccurred: '抱歉，发生了错误，请稍后重试。',
      },
      useFloatAgent: {
        errorOccurred: '抱歉，发生了错误，请稍后重试。',
      },
      useGemini: {
        errorOccurred: '抱歉，发生了错误，请稍后重试。',
        unknownError: '未知错误',
      },
      useGlm: {
        errorOccurred: '抱歉，发生了错误，请稍后重试。',
        unknownError: '未知错误',
      },
      useGrok: {
        errorOccurred: '抱歉，发生了错误，请稍后重试。',
      },
      useKnowledge: {
        knowledgeBaseCreated: '知识库已创建',
        createKnowledgeBaseFailed: '创建失败',
        knowledgeBaseUpdated: '知识库 {name} 已更新',
        updateKnowledgeBaseFailed: '更新知识库失败',
        knowledgeBaseDeleted: '知识库已删除',
        deleteKnowledgeBaseFailed: '删除知识库失败',
        getKnowledgeBaseEmptyData: '获取知识库详情失败',
        loadKnowledgeBaseDetailFailed: '加载知识库详情失败',
        knowledgeDocumentAdded: '文档 {title} 已添加到知识库',
        createKnowledgeDocFailed: '创建知识库文档失败',
        knowledgeDocumentUpdated: '知识库文档已更新',
        updateKnowledgeDocFailed: '更新知识库文档失败',
        knowledgeDocumentDeleted: '知识库文档已删除',
        deleteKnowledgeDocFailed: '删除知识库文档失败',
        addDocumentToKBFailed: '添加文档到知识库失败',
        documentRemovedFromKB: '文档已从知识库移除',
        removeDocumentFromKBFailed: '从知识库移除文档失败',
        searchKnowledgeFailed: '搜索知识库失败',
        searchFailed: '搜索失败: {error}',
        batchSearchCompleted:
          '批量搜索完成，在 {knowledgeBaseIdsLength} 个知识库中找到 {totalResults} 个相关结果',
        batchSearchNoResults: '在 {knowledgeBaseIdsLength} 个知识库中未找到相关内容',
        batchSearchFailed: '批量搜索知识库失败',
        batchSearchFailedMessage: '批量搜索知识库失败: {error}',
        getKnowledgeBaseStatsFailed: '获取知识库统计数据失败',
        searchTimeout: '搜索超时',
        searchTimeoutCaption: '可能是向量化配置问题，已降级为文本匹配搜索',
        embeddingGenerated: '向量生成成功: {dimension}维',
        embeddingGeneratedFailed: '向量生成失败: {error}',
        embeddingGeneratedError: '向量生成异常: {error}',
        chunkVectorUpdated: '文档片段向量更新成功: {message}',
        chunkVectorUpdatedCaption: '更新片段数: {updated_chunks}',
        chunkVectorUpdatedFailed: '片段向量更新失败: {error}',
        chunkVectorUpdatedError: '片段向量更新异常: {error}',
        embeddingApiTestSuccess: 'Embedding API测试成功',
        embeddingApiTestSuccessCaption: '维度: {dimension}',
        embeddingApiTestFailed: 'Embedding API测试失败: {error}',
        embeddingApiTestError: '测试embedding API时出错',
        multiSemanticEmbeddingTestSuccess: '多重语义向量化测试成功',
        multiSemanticEmbeddingTestSuccessCaption:
          '策略: {processing_strategy}, 维度: {embedding_dimension}',
        multiSemanticEmbeddingTestFailed: '多重语义向量化测试失败: {error}',
        multiSemanticEmbeddingTestError: '测试多重语义向量化时出错',
        documentChunksRegenerated: '文档片段重新生成成功: {message}',
        documentChunksRegeneratedCaption: '删除旧片段: {old_chunks}, 创建新片段: {new_chunks}',
        documentChunksRegenerationFailed: '重新生成失败: {error}',
        objectBoxConnectionTestSuccess: 'ObjectBox连接正常: {details}',
        objectBoxConnectionTestFailed: 'ObjectBox连接失败: {error}',
        objectBoxConnectionTestError: '连接测试失败: {error}',
      },
      useMiniMax: {
        toolExecutionFailed: '工具执行失败: {error}',
        errorOccurred: '抱歉，发生了错误，请稍后重试。',
      },
      useMoonshot: {
        toolExecutionFailed: '工具执行失败: {error}',
        errorOccurred: '抱歉，发生了错误，请稍后重试。',
      },
      useOllama: {
        toolExecutionFailed: '工具执行失败: {error}',
        errorOccurred: '抱歉，发生了错误，请稍后重试。',
      },
      useOpenAI: {
        toolExecutionFailed: '工具执行失败: {error}',
        errorOccurred: '抱歉，发生了错误，请稍后重试。',
      },
      useQwen: {
        toolExecutionFailed: '工具执行失败: {error}',
        errorOccurred: '抱歉，发生了错误，请稍后重试。',
      },
      useVolces: {
        toolExecutionFailed: '工具执行失败: {error}',
        errorOccurred: '抱歉，发生了错误，请稍后重试。',
      },
      useKnowledgeDocumentBatch: {
        create_document_failed: '创建知识库文档失败',
        submit_chunking_result_failed: '提交切割结果失败',
        chunking_failed: '文档切割失败',
        document_vectorized_successfully: '文档向量化完成',
        document_vectorized_caption: '文档ID: {docId}，生成了 {chunkCount} 个知识片段',
        document_vectorization_failed: '文档向量化失败',
        document_id: '文档ID',
        processing_document: '正在处理文档: {title}',
        document_processed_successfully: '文档处理成功: {title}',
        batch_processing_completed: '批量处理完成，成功处理 {successCount} 个文档',
        batch_processing_failed: '批量处理失败',
        no_documents_to_process: '没有找到需要处理的文档',
        get_folder_documents_failed: '获取文件夹文档失败',
      },
      useDocumentChunking: {
        chunking_completed: '文档 "{title}" 切割完成，共 {count} 个块',
        chunking_failed: '文档 "{title}" 切割失败: {error}',
        process_failed: '处理文档 "{title}" 失败: {error}',
        vectorization_completed: '文档 {docId} 向量化完成，共处理 {chunkCount} 个块',
        vectorization_failed: '文档 {docId} 向量化失败',
        revectorize_failed: '重新向量化失败: {error}',
      },
      useDocumentToKnowledge: {
        batch_completed: '批量处理完成：成功 {processed}/{total}，失败 {errors} 个',
        revectorize_completed: '文档"{title}"重新向量化完成，生成 {chunks} 个文本块',
        revectorize_failed: '重新向量化失败: {error}',
      },
    },
    composables: {
      useErrorHandler: {
        retry: '重试',
        close: '关闭',
        editorLoad: '编辑器加载失败，请刷新页面重试',
        contentSync: '内容同步失败，您的更改可能未保存',
        dragOperation: '拖拽操作失败，请重试',
        fileTree: '文件树加载失败，请刷新页面',
        saveOperation: '保存失败，请检查网络连接后重试',
        operationFailed: '操作失败，请重试',
      },
      useKnowledgeBaseManager: {
        loadKnowledgeBaseFailed: '加载知识库失败',
        knowledgeBaseCreated: '知识库 {name} 创建成功',
        knowledgeBaseCreationFailed: '创建知识库失败',
        knowledgeBaseUpdated: '知识库 {name} 更新成功',
        knowledgeBaseUpdateFailed: '更新知识库失败',
        knowledgeBaseDeleted: '知识库已删除',
        knowledgeBaseDeletionFailed: '删除知识库失败',
        documentDeleted: '文档删除成功',
        documentVectorized: '文档向量化完成',
        documentVectorizedCaption: '文档ID: {docId}, 生成了 {chunkCount} 个知识片段',
        documentVectorizationFailed: '文档向量化失败',
        documentVectorizationFailedCaption: '文档ID: {docId}',
        documentDeletionFailed: '删除文档失败',
        documentDeletionFailedCaption: '文档ID: {docId}',
      },
      useLoadingState: {
        loading: '加载中...',
        loadingTimeout: '加载超时',
        operationFailed: '操作失败',
      },
    },
    llm: {
      tools: {
        editor: {
          findAllMatches: {
            foundMatches: '找到 {count} 个匹配结果',
            noMatches: '未找到文本 "{searchText}"',
            error: '搜索时发生错误: {error}',
          },
          verifyOperation: {
            missingParams: '缺少验证参数',
            replaceAddedTrackChange: '替换操作已添加TrackChange标记，等待用户确认',
            replaceAddedTrackChangeDetails: '文本"{originalText}"已标记为替换，等待用户确认后生效',
            replaceCompleted: '替换操作已完成',
            replaceCompletedDetails:
              '原文本"{originalText}"已标记为删除，新文本"{newText}"已标记为插入',
            replaceFailed: '替换操作验证失败',
            replaceFailedDetails: '未能在文档中找到替换的文本"{newText}"',
            missingInsertText: '缺少插入文本参数',
            insertCompleted: '插入操作已完成',
            insertCompletedDetails: '文本"{newText}"已成功插入',
            insertFailed: '插入操作验证失败',
            insertFailedDetails: '未能在文档中找到插入的文本"{newText}"',
            missingDeleteText: '缺少删除文本参数',
            deleteAddedTrackChange: '删除操作已添加TrackChange标记，等待用户确认',
            deleteAddedTrackChangeDetails: '文本"{originalText}"已标记为删除，等待用户确认后生效',
            deleteCompleted: '删除操作已完成',
            deleteCompletedDetails: '文本"{originalText}"已被删除',
            unknownOperation: '未知的操作类型',
            error: '验证过程中发生错误',
            searchAndReplace: {
              noEditorInstance: '未找到文档ID {documentId} 对应的编辑器实例',
              noActiveEditor: '未找到活动的编辑器实例，请确保有打开的文档',
              noMatches: '未找到文本 "{searchText}"',
              occurrenceOutOfRange:
                '指定的匹配序号 {occurrence} 超出范围，共找到 {totalMatches} 个匹配结果（序号从0开始）',
              multipleMatches:
                '找到 {totalMatches} 个匹配结果，请指定要替换的occurrence参数：\n{matchList}',
              deleteComment: 'AI建议删除: "{targetMatchText}"',
              insertComment: 'AI建议添加: "{replaceText}"',
              replaceCompleted: '成功将 "{searchText}" 替换为 "{replaceText}"',
              replaceFailed: '替换操作失败，请重试',
              error: '替换操作时发生错误: {error}',
            },
            searchAndInsert: {
              noEditorInstance: '未找到文档ID {documentId} 对应的编辑器实例',
              noActiveEditor: '未找到活动的编辑器实例，请确保有打开的文档',
              noMatches: '未找到文本 "{searchText}"',
              occurrenceOutOfRange:
                '指定的匹配序号 {occurrence} 超出范围，共找到 {totalMatches} 个匹配结果（序号从0开始）',
              multipleMatches:
                '找到 {totalMatches} 个匹配结果，请指定要插入的occurrence参数：\n{matchList}',
              positionOutOfRange: '字符位置 {position} 超出范围，搜索文本长度为 {searchTextLength}',
              invalidPosition: '无效的插入位置，请使用 "before"、"after" 或数字索引',
              insertComment: 'AI建议添加: "{insertText}"',
              insertCompleted: '成功在 "{searchText}" {positionDesc}插入 "{insertText}"',
              insertFailed: '插入操作失败，请重试',
              error: '插入操作时发生错误: {error}',
              before: '之前',
              after: '之后',
              positionDesc: '第{position}个字符位置',
            },
            searchAndDelete: {
              noEditorInstance: '未找到文档ID {documentId} 对应的编辑器实例',
              noActiveEditor: '未找到活动的编辑器实例，请确保有打开的文档',
              noMatches: '未找到文本 "{searchText}"',
              occurrenceOutOfRange:
                '指定的匹配序号 {occurrence} 超出范围，共找到 {totalMatches} 个匹配结果（序号从0开始）',
              multipleMatches:
                '找到 {totalMatches} 个匹配结果，请指定要删除的occurrence参数：\n{matchList}',
              deleteComment: 'AI建议删除: "{targetMatchText}"',
              deleteCompleted: '成功删除 "{searchText}"',
              deleteFailed: '删除操作失败，请重试',
              error: '删除操作时发生错误: {error}',
            },
          },
          common: {
            noEditorInstance: '未找到文档ID {documentId} 对应的编辑器实例',
            noActiveEditor: '未找到活动的编辑器实例，请确保有打开的文档',
          },
          getDocumentInfo: {
            success: '成功获取文档信息',
            cannotDetermineWindow: '无法确定文档的窗口信息',
            error: '获取文档信息时发生错误: {error}',
          },
          hasPendingChanges: {
            found: '发现 {count} 个待处理的AI修改',
            none: '没有待处理的AI修改',
            error: '检测修改时发生错误: {error}',
          },
          acceptAllChanges: {
            noEditorInstance: '未找到文档ID {documentId} 对应的编辑器实例',
            noActiveEditor: '未找到活动的编辑器实例',
            success: '已接受 {count} 个AI修改',
            failed: '接受修改失败，请重试',
            error: '接受修改时发生错误: {error}',
          },
          rejectAllChanges: {
            noEditorInstance: '未找到文档ID {documentId} 对应的编辑器实例',
            noActiveEditor: '未找到活动的编辑器实例',
            success: '已拒绝 {count} 个AI修改',
            failed: '拒绝修改失败，请重试',
            error: '拒绝修改时发生错误: {error}',
          },
          formatAndResetDocument: {
            noEditorInstance: '未找到文档ID {documentId} 对应的编辑器实例',
            noActiveEditor: '未找到活动的编辑器实例，请确保有打开的文档',
            emptyContent: '格式化内容不能为空',
            success: '文档格式化完成，内容从 {originalLength} 字符更新为 {newLength} 字符',
            verified: '文档内容已成功重设为格式化后的内容',
            details: '原文档长度: {originalLength} 字符，新文档长度: {newLength} 字符',
            failed: '文档格式化失败，请重试',
            error: '文档格式化时发生错误: {error}',
          },
          searchText: {
            error: '搜索时发生错误: {error}',
          },
        },
        file: {
          unnamedFolder: '未命名文件夹',
          unnamedDocument: '未命名文档',
          getFileTree: {
            success:
              '获取到文件树结构，共 {folderCount} 个文件夹，{documentCount} 个文档;完整结构数据：{detail}',
            failed: '获取文件树失败: {error}',
          },
          createDocument: {
            emptyTitle: '文档标题不能为空',
            folderNotFound: '找不到ID为 {folderId} 的文件夹',
            success: '已创建文档 "{title}" 在文件夹 "{folderName}" 中',
            error: '创建文档时发生错误: {error}',
          },
          deleteDocuments: {
            emptyIds: '文档ID数组不能为空',
            idsNotArray: 'documentIds必须是数组格式',
            notFound: '找不到以下ID的文档: {ids}',
            noneFound: '没有找到可删除的文档',
            success: '批量删除完成',
            error: '批量删除文档时发生错误: {error}',
          },
          renameDocument: {
            emptyTitle: '新的文档标题不能为空',
            notFound: '找不到ID为 {documentId} 的文档',
            sameTitle: '新标题与当前标题相同，无需修改',
            success: '文档 "{oldTitle}" 已经重命名为 "{newTitle}"，详细信息： {details}',
            error: '重命名文档时发生错误: {error}',
          },
          searchDocuments: {
            emptyText: '搜索文本不能为空',
            success: '搜索 "{searchText}" 完成，找到 {count} 个匹配结果, 详细信息： {details}',
            error: '搜索时发生错误: {error}',
          },
          createFolder: {
            emptyName: '文件夹名称不能为空',
            parentNotFound: '找不到ID为 {parentId} 的父文件夹',
            success:
              '文件夹 "{name}"创建成功， 文件夹ID "{folderId}"， 父文件夹名称 "{parentName}"， 父文件夹ID "{parentId}"',
            error: '创建文件夹时发生错误: {error}',
          },
          deleteFolder: {
            notFound: '找不到ID为 {folderId} 的文件夹',
            hasChildren:
              '无法删除文件夹 "{folderName}"，因为它包含 {count} 个子项目；这是一个高风险操作，请用户自行处理',
            success: '已删除文件夹 "{folderName}"',
            error: '删除文件夹时发生错误: {error}',
          },
          renameFolder: {
            emptyName: '新的文件夹名称不能为空',
            notFound: '找不到ID为 {folderId} 的文件夹',
            sameName: '新名称与当前名称相同，无需修改',
            success: '文件夹 "{oldName}" 已经重命名为 "{newName}"',
            error: '重命名文件夹时发生错误: {error}',
          },
          openDocument: {
            noDocumentId: '请提供文档ID',
            notFound: '找不到ID为 {documentId} 的文档',
            noFolder: '文档 {documentId} 没有关联的文件夹',
            folderMismatch:
              '文档 {documentId} 不在指定的文件夹 {folderId} 中，实际位于文件夹 {actualFolderId}',
            folderNotFound: '找不到ID为 {folderId} 的文件夹',
            documentNotFound: '在文件夹 "{folderName}" 中找不到ID为 {documentId} 的文档',
            success: '文档已经打开: id - {documentId}, 标题 - {title}',
            error: '打开文档时发生错误: {error}',
          },
          updateDocumentContent: {
            emptyContent: '文档内容参数不能为undefined或null',
            notFound: '找不到ID为 {documentId} 的文档',
            success: '文档内容更新成功',
            error: '修改文档内容时发生错误: {error}',
          },
          searchFolders: {
            emptyKeyword: '搜索关键字不能为空',
            success: '使用关键字："{keyword}"搜索到{count}个文件夹，详细信息：{details}',
            error: '搜索文件夹时发生错误: {error}',
          },
        },
        knowledgeBase: {
          noKnowledgeBaseSelected:
            '未选择知识库，请先在对话界面选择要搜索的知识库，或在工具参数中指定 knowledge_base_id',
          searchResult:
            '在知识库"{knowledgeBaseName}"中搜索"{query}"，找到 {resultCount} 个相关结果：\n\n',
          noResult: '在知识库"{knowledgeBaseName}"中未找到与"{query}"相关的内容。',
          resultItem: '**{index}. {title}**\n相似度: {score}%\n内容摘要: {summary}{ellipsis}\n\n',
          relatedContent: '相关内容',
          resultFooter: '以上是从知识库中检索到的相关信息，请基于这些内容来回答用户的问题。',
          error: '知识库搜索失败: {error}',
          knowledgeBaseName: '知识库 {id}',
        },
        pexels: {
          noPexelsResourceProvider: '未配置Pexels资源服务商，请在设置中配置',
          pexelsApiCredentialsNotConfigured: 'Pexels API凭据未配置，请在设置中配置',
          pexelsApiRequestFailed: 'Pexels API请求失败: {status}, {statusText}',
          successfullyFetchedImages: '成功获取到 {count} 张图片，搜索关键词: {query}',
          pexelsSearchFailed: 'Pexels搜索失败: {error}',
          pexelsVideoApiRequestFailed: 'Pexels视频API请求失败: {status}, {statusText}',
          pexelsVideoSearchFailed: 'Pexels视频搜索失败: {error}',
          successfullyFetchedVideos: '成功获取到 {count} 个视频，搜索关键词: {query}',
        },
        search: {
          noTavilyResourceProvider: '未配置Tavily搜索引擎，请在设置中配置',
          tavilyApiKeyNotConfigured: 'Tavily API Key 未配置。请在设置中配置您的 Tavily API Key。',
          searchRequestFailed: '搜索请求失败: {status}, {statusText}',
          searchCompleted: '搜索完成，找到 {count} 个结果',
          searchFailed: '搜索失败: {error}',
          directAnswer: '直接答案: {answer}',
        },
      },
    },
  },
};
