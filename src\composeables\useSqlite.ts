import { readonly, ref } from 'vue';
import type { JSONContent } from '@tiptap/vue-3';
import { useUtils } from './useUtils';
import type { Folder, Document } from 'src/types/doc';
import type { DatabaseApi, AppSettings, LlmSettings } from 'src/env.d';
import type { Conversation } from 'src/types/qwen';

const { safeJsonStringify } = useUtils();

// 使用模块声明来处理全局类型
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $databaseApi: DatabaseApi;
  }
}

// 参数安全验证工具函数
const validateParams = {
  /**
   * 确保ID参数是有效整数，null值转换为-1
   * @param value 输入值
   * @param defaultValue 默认值，当输入无效时使用
   * @returns 安全的ID值，-1表示null
   */
  safeId: (value: number | null | undefined, defaultValue: number = -1): number => {
    if (value === null || value === undefined) {
      return defaultValue;
    }
    return Number.isInteger(value) && value >= 0 ? value : defaultValue;
  },

  /**
   * 确保数字参数是有效整数
   * @param value 输入值
   * @param defaultValue 默认值
   * @returns 安全的数字值
   */
  safeNumber: (value: number | null | undefined, defaultValue: number = -1): number => {
    if (value === null || value === undefined) {
      return defaultValue;
    }
    return Number.isInteger(value) ? value : defaultValue;
  },
};

// 初始化状态现在由 composable 内部管理，但依赖外部 promise
const isInitialized = ref(false);
let db: DatabaseApi | null = null;

export function useSqlite() {
  // 辅助函数：将错误转换为 Error 对象
  const toError = (err: unknown): Error => {
    if (err instanceof Error) {
      return err;
    }
    return new Error('操作失败');
  };

  // 辅助函数：将 null/undefined 转换为 -1
  // 注意: Qt端现在直接支持null值，无需前端转换

  /**
   * 等待由 qt-integration.ts 管理的全局 QWebChannel 初始化完成。
   * @returns {Promise<void>}
   */
  const initialize = async (): Promise<void> => {
    if (isInitialized.value && db) {
      console.log('✅ [useSqlite] 数据库已初始化，跳过重复初始化');
      return Promise.resolve();
    }

    console.log('🔄 [useSqlite] 开始初始化数据库...');

    if (window.qtInitialized === undefined) {
      const error =
        'Qt initialization promise not found. Is the qt-integration boot file configured correctly?';
      console.error('❌ [useSqlite]', error);
      throw new Error(error);
    }

    console.log('⏳ [useSqlite] 等待Qt初始化完成...');
    await window.qtInitialized;
    console.log('✅ [useSqlite] Qt初始化完成');

    if (window.databaseApi === undefined) {
      const error = 'window.databaseApi object not found after Qt initialization.';
      console.error('❌ [useSqlite]', error);
      throw new Error(error);
    }

    // 初始化成功后，设置 db 变量
    db = window.databaseApi;
    isInitialized.value = true;
    console.log('✅ [useSqlite] 数据库初始化完成');
  };

  /**
   * 获取文档
   */
  const getDocument = async (id: number) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<Document>((resolve, reject) => {
      try {
        db.getDocument(id, (result) => {
          // console.log('getDocument', result);
          if (!result.success) {
            reject(new Error(result.error || 'get document failed'));
            return;
          }

          resolve(result);
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 获取指定文件夹及其所有子文件夹下的文档ID列表
   */
  const getAllDocumentsInFolder = async (folder_id: number) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<number[]>((resolve, reject) => {
      try {
        db.getAllDocumentsInFolder(folder_id, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'get all documents in folder failed'));
            return;
          }
          // 返回文档ID数组
          resolve(result.document_ids || []);
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 创建文档
   */
  const createDocument = async (
    title: string,
    content: JSONContent,
    folder_id: number | null,
    metadata: string = '{}',
  ) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<number>((resolve, reject) => {
      try {
        // metadata 参数已经是 JSON 字符串，不需要再次序列化
        const metadataJson = metadata;

        // 序列化 content，可能很大，需要特别处理
        let contentJson: string;
        try {
          contentJson = safeJsonStringify(content);
        } catch (contentError) {
          if (contentError instanceof Error) {
            // 如果是内容过大的错误，提供更详细的信息
            if (
              contentError.message.includes('内容过大') ||
              contentError.message.includes('系统限制')
            ) {
              const error = new Error(
                `文档创建失败：${contentError.message}\n\n建议：\n1. 删除或压缩文档中的大型图片\n2. 分割长文档为多个较小的文档\n3. 清理不必要的格式和内容`,
              );
              reject(error);
              return;
            }
          }
          // 其他序列化错误
          reject(
            new Error(
              `文档内容序列化失败：${contentError instanceof Error ? contentError.message : '未知错误'}`,
            ),
          );
          return;
        }

        // 使用工具函数确保参数安全
        const safeFolderId = validateParams.safeId(folder_id);
        db.createDocument(title, contentJson, safeFolderId, metadataJson, (result) => {
          // console.log('createDocument', result);
          if (!result.success) {
            reject(new Error(result.error || 'create document failed'));
            return;
          }
          resolve(result.id);
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 更新文档
   */
  const updateDocument = async (
    id: number,
    title: string,
    content: JSONContent,
    folder_id: number | null,
    metadata: string = '{}',
  ) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<void>((resolve, reject) => {
      try {
        // metadata 参数已经是 JSON 字符串，不需要再次序列化
        const metadataJson = metadata;

        // 序列化 content，可能很大，需要特别处理
        let contentJson: string;
        try {
          contentJson = safeJsonStringify(content);
        } catch (contentError) {
          if (contentError instanceof Error) {
            // 如果是内容过大的错误，提供更详细的信息
            if (
              contentError.message.includes('内容过大') ||
              contentError.message.includes('系统限制')
            ) {
              const error = new Error(
                `文档保存失败：${contentError.message}\n\n建议：\n1. 删除或压缩文档中的大型图片\n2. 分割长文档为多个较小的文档\n3. 清理不必要的格式和内容`,
              );
              reject(error);
              return;
            }
          }
          // 其他序列化错误
          reject(
            new Error(
              `文档内容序列化失败：${contentError instanceof Error ? contentError.message : '未知错误'}`,
            ),
          );
          return;
        }

        // 使用工具函数确保参数安全
        const safeFolderId = validateParams.safeId(folder_id);

        db.updateDocument(id, title, contentJson, safeFolderId, metadataJson, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'update document failed'));
            return;
          }
          resolve();
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 重命名文档（只修改标题）
   */
  const renameDocument = async (id: number, newTitle: string) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<void>((resolve, reject) => {
      try {
        // 检查方法是否存在
        if (typeof db.renameDocument !== 'function') {
          console.warn('⚠️ [useSqlite] renameDocument 方法不存在，可能需要重新编译Qt项目');
          reject(new Error('API方法不可用，请重新编译Qt项目'));
          return;
        }

        console.log('📝 [useSqlite] 重命名文档, id:', id, 'newTitle:', newTitle);

        // 调用同步方法获取结果
        const resultJson = db.renameDocument(id, newTitle);
        let finalResult: string;

        if (typeof resultJson === 'string') {
          finalResult = resultJson;
        } else {
          console.error('❌ [useSqlite] Unexpected return type:', typeof resultJson);
          reject(new Error('Unexpected return type from Qt method'));
          return;
        }

        const result = JSON.parse(finalResult);

        if (!result.success) {
          console.error('❌ [useSqlite] renameDocument failed:', result.error);
          reject(new Error(result.error || 'rename document failed'));
          return;
        }

        console.log('✅ [useSqlite] renameDocument succeeded');
        resolve();
      } catch (err) {
        console.error('❌ [useSqlite] renameDocument error:', err);
        reject(toError(err));
      }
    });
  };

  /**
   * 添加文档知识库关联
   */
  const addDocumentKnowledgeAssociation = async (
    documentId: number,
    knowledgeDocumentId: number,
    knowledgeBaseId: number,
  ) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<{ success: boolean; message: string; association_id?: number }>(
      (resolve, reject) => {
        try {
          db.addDocumentKnowledgeAssociation(
            documentId,
            knowledgeDocumentId,
            knowledgeBaseId,
            (result) => {
              if (!result.success) {
                reject(new Error(result.error || 'add document knowledge association failed'));
                return;
              }
              resolve(result);
            },
          );
        } catch (err) {
          reject(toError(err));
        }
      },
    );
  };

  /**
   * 移除文档知识库关联
   */
  const removeDocumentKnowledgeAssociation = async (
    documentId: number,
    knowledgeBaseId: number,
  ) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<{ success: boolean; message: string; removed_count?: number }>(
      (resolve, reject) => {
        try {
          db.removeDocumentKnowledgeAssociation(documentId, knowledgeBaseId, (result) => {
            if (!result.success) {
              reject(new Error(result.error || 'remove document knowledge association failed'));
              return;
            }
            resolve(result);
          });
        } catch (err) {
          reject(toError(err));
        }
      },
    );
  };

  /**
   * 获取文档的所有知识库关联
   */
  const getDocumentKnowledgeAssociations = async (documentId: number) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<
      Array<{
        id: number;
        knowledge_document_id: number;
        knowledge_base_id: number;
        created_at: string;
      }>
    >((resolve, reject) => {
      try {
        db.getDocumentKnowledgeAssociations(documentId, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'get document knowledge associations failed'));
            return;
          }
          resolve(result.associations || []);
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 根据知识库文档ID获取关联的原始文档
   */
  const getOriginalDocumentByKnowledgeDocumentId = async (knowledgeDocumentId: number) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<{
      success: boolean;
      id?: number;
      title?: string;
      content?: string;
      metadata?: string;
      created_at?: string;
      updated_at?: string;
      message?: string;
      error?: string;
    }>((resolve, reject) => {
      try {
        // 检查方法是否存在
        if (typeof db.getOriginalDocumentByKnowledgeDocumentId !== 'function') {
          console.warn(
            '⚠️ [useSqlite] getOriginalDocumentByKnowledgeDocumentId 方法不存在，可能需要重新编译Qt项目',
          );
          resolve({
            success: false,
            message: 'API方法不可用，请重新编译Qt项目',
          });
          return;
        }

        db.getOriginalDocumentByKnowledgeDocumentId(knowledgeDocumentId, (result) => {
          resolve(result);
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 检查文档是否在特定知识库中
   */
  const isDocumentInKnowledgeBase = async (documentId: number, knowledgeBaseId: number) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<boolean>((resolve, reject) => {
      try {
        db.isDocumentInKnowledgeBase(documentId, knowledgeBaseId, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'check document in knowledge base failed'));
            return;
          }
          resolve(result.exists || false);
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 检查知识库文档是否需要重新向量化
   * 判断条件：
   * 1. 该知识库文档是从原始文档添加而来（存在关联关系）
   * 2. 原始文档的更新时间晚于知识库文档的更新时间
   */
  const checkKnowledgeDocumentNeedsRevectorization = async (
    knowledgeDocumentId: number,
    knowledgeDocuments: Array<{ id: number; updated_at: string | number }>,
  ) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    try {
      console.log('🔍 [useSqlite] 检查知识库文档是否需要重新向量化:', knowledgeDocumentId);

      // 1. 获取关联的原始文档
      const originalDocResult = await getOriginalDocumentByKnowledgeDocumentId(knowledgeDocumentId);

      if (!originalDocResult.success || !originalDocResult.content) {
        // 没有找到关联的原始文档，说明不是从原始文档添加的
        console.log('📋 [useSqlite] 知识库文档无关联的原始文档:', knowledgeDocumentId);
        return {
          needsRevectorization: false,
          reason: 'No original document association found',
        };
      }

      // 2. 获取知识库文档的更新时间
      const knowledgeDoc = knowledgeDocuments.find((doc) => doc.id === knowledgeDocumentId);
      if (!knowledgeDoc) {
        console.warn('⚠️ [useSqlite] 未找到知识库文档:', knowledgeDocumentId);
        return {
          needsRevectorization: false,
          reason: 'Knowledge document not found in provided list',
        };
      }

      // 3. 转换时间格式并标准化
      const originalUpdatedAt = originalDocResult.updated_at;
      let knowledgeUpdatedAt: string;

      if (typeof knowledgeDoc.updated_at === 'number') {
        // 如果是时间戳，转换为ISO字符串
        knowledgeUpdatedAt = new Date(knowledgeDoc.updated_at).toISOString();
      } else {
        knowledgeUpdatedAt = knowledgeDoc.updated_at;
      }

      // 标准化时间格式 - 确保都能被正确解析
      const normalizeTimeString = (timeStr: string): string => {
        // 如果是 SQLite 格式 (YYYY-MM-DD HH:MM:SS)，转换为 ISO 格式
        if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(timeStr)) {
          return timeStr.replace(' ', 'T') + 'Z';
        }
        // 如果已经是 ISO 格式，直接返回
        return timeStr;
      };

      const normalizedOriginalTime = normalizeTimeString(originalUpdatedAt);
      const normalizedKnowledgeTime = normalizeTimeString(knowledgeUpdatedAt);

      console.log('🔄 [useSqlite] 时间格式标准化:', {
        original: { raw: originalUpdatedAt, normalized: normalizedOriginalTime },
        knowledge: { raw: knowledgeUpdatedAt, normalized: normalizedKnowledgeTime },
      });

      // 4. 比较时间戳
      console.log('🕐 [useSqlite] 时间比较详情:', {
        originalUpdatedAt,
        knowledgeUpdatedAt,
        normalizedOriginalTime,
        normalizedKnowledgeTime,
        originalFormat: typeof originalUpdatedAt,
        knowledgeFormat: typeof knowledgeUpdatedAt,
      });

      const originalTime = new Date(normalizedOriginalTime);
      const knowledgeTime = new Date(normalizedKnowledgeTime);

      console.log('🕐 [useSqlite] 解析后的时间对象:', {
        originalTime: originalTime.toISOString(),
        knowledgeTime: knowledgeTime.toISOString(),
        originalTimestamp: originalTime.getTime(),
        knowledgeTimestamp: knowledgeTime.getTime(),
        originalIsValid: !isNaN(originalTime.getTime()),
        knowledgeIsValid: !isNaN(knowledgeTime.getTime()),
      });

      // 检查时间是否有效
      if (isNaN(originalTime.getTime()) || isNaN(knowledgeTime.getTime())) {
        console.error('❌ [useSqlite] 时间解析失败:', {
          originalUpdatedAt,
          knowledgeUpdatedAt,
          originalTime: originalTime.toString(),
          knowledgeTime: knowledgeTime.toString(),
        });
        return {
          needsRevectorization: false,
          reason: 'Invalid timestamp format',
        };
      }

      const needsRevectorization = originalTime > knowledgeTime;
      const timeDifference = originalTime.getTime() - knowledgeTime.getTime();

      const result = {
        needsRevectorization,
        originalDocumentId: originalDocResult.id,
        originalUpdatedAt: normalizedOriginalTime,
        knowledgeUpdatedAt: normalizedKnowledgeTime,
        reason: needsRevectorization
          ? `Original document updated after knowledge document (original: ${normalizedOriginalTime}, knowledge: ${normalizedKnowledgeTime}, diff: ${timeDifference}ms)`
          : `Knowledge document is up to date (original: ${normalizedOriginalTime}, knowledge: ${normalizedKnowledgeTime}, diff: ${timeDifference}ms)`,
      };

      console.log('📊 [useSqlite] 重新向量化检查结果:', {
        knowledgeDocumentId,
        needsRevectorization,
        originalTime: originalTime.toISOString(),
        knowledgeTime: knowledgeTime.toISOString(),
        timeDifferenceMs: timeDifference,
        timeDifferenceSeconds: Math.round(timeDifference / 1000),
      });

      return result;
    } catch (error) {
      console.error('❌ [useSqlite] 检查重新向量化失败:', error);
      return {
        needsRevectorization: false,
        reason: `Check failed: ${error instanceof Error ? error.message : '未知错误'}`,
      };
    }
  };

  /**
   * 清除知识库文档的关联信息（调用后端API）
   */
  const clearDocumentKnowledgeAssociation = async (
    knowledgeDocumentId: number,
  ): Promise<{
    success: boolean;
    message: string;
    cleared_count?: number;
    cleared_documents?: Array<{ id: number; title: string }>;
  }> => {
    await initialize();
    if (!db) {
      throw new Error('Database not initialized');
    }

    return new Promise((resolve, reject) => {
      try {
        console.log('🔄 [useSqlite] 调用DatabaseApi清除知识库文档关联:', knowledgeDocumentId);

        db.clearDocumentKnowledgeAssociation(knowledgeDocumentId.toString(), (result) => {
          console.log('✅ [useSqlite] DatabaseApi返回结果:', result);

          if (result.success) {
            resolve(result);
          } else {
            reject(new Error(result.error || 'Clear document knowledge association failed'));
          }
        });
      } catch (error) {
        console.error('❌ [useSqlite] 调用DatabaseApi失败:', error);
        reject(error instanceof Error ? error : new Error(String(error)));
      }
    });
  };

  /**
   * 清除知识库的所有文档关联信息（调用后端API）
   */
  const clearKnowledgeBaseAssociations = async (
    knowledgeBaseId: number,
  ): Promise<{
    success: boolean;
    message: string;
    cleared_count?: number;
  }> => {
    await initialize();
    if (!db) {
      throw new Error('Database not initialized');
    }

    return new Promise((resolve, reject) => {
      try {
        // 检查方法是否存在
        if (typeof db.clearKnowledgeBaseAssociations !== 'function') {
          console.warn(
            '⚠️ [useSqlite] clearKnowledgeBaseAssociations 方法不存在，可能需要重新编译Qt项目',
          );
          resolve({
            success: false,
            message: 'API方法不可用，请重新编译Qt项目',
          });
          return;
        }

        console.log('🔄 [useSqlite] 调用DatabaseApi清除知识库所有文档关联:', knowledgeBaseId);

        db.clearKnowledgeBaseAssociations(knowledgeBaseId, (result) => {
          console.log('✅ [useSqlite] DatabaseApi返回结果:', result);

          if (result.success) {
            resolve(result);
          } else {
            reject(new Error(result.error || 'Clear knowledge base associations failed'));
          }
        });
      } catch (error) {
        console.error('❌ [useSqlite] 调用DatabaseApi失败:', error);
        reject(error instanceof Error ? error : new Error(String(error)));
      }
    });
  };

  /**
   * 更新知识库文档关联时间戳（调用后端API）
   */
  const updateKnowledgeDocumentAssociationTime = async (
    knowledgeDocumentId: number,
  ): Promise<{
    success: boolean;
    message: string;
    updated_count?: number;
  }> => {
    await initialize();
    if (!db) {
      throw new Error('Database not initialized');
    }

    return new Promise((resolve, reject) => {
      try {
        // 检查方法是否存在
        if (typeof db.updateKnowledgeDocumentAssociationTime !== 'function') {
          console.warn(
            '⚠️ [useSqlite] updateKnowledgeDocumentAssociationTime 方法不存在，可能需要重新编译Qt项目',
          );
          resolve({
            success: false,
            message: 'API方法不可用，请重新编译Qt项目',
          });
          return;
        }

        console.log('🔄 [useSqlite] 调用DatabaseApi更新知识库文档关联时间:', knowledgeDocumentId);

        db.updateKnowledgeDocumentAssociationTime(knowledgeDocumentId, (result) => {
          console.log('✅ [useSqlite] DatabaseApi返回结果:', result);

          if (result.success) {
            resolve(result);
          } else {
            reject(new Error(result.error || 'Update knowledge document association time failed'));
          }
        });
      } catch (error) {
        console.error('❌ [useSqlite] 调用DatabaseApi失败:', error);
        reject(error instanceof Error ? error : new Error(String(error)));
      }
    });
  };

  /**
   * 根据 ID 删除一篇文档
   */
  const deleteDocument = async (id: number) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    // console.log('deleteDocument', id);
    return new Promise<void>((resolve, reject) => {
      try {
        db.deleteDocument(id, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'delete document failed'));
            return;
          }
          resolve();
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 创建文件夹
   */
  const createFolder = async (name: string, parent_id?: number | null) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    // console.log('createFolder', name, toFolderId(parent_id));
    return new Promise<number>((resolve, reject) => {
      try {
        // 使用工具函数确保参数安全
        const safeParentId = validateParams.safeId(parent_id);
        db.createFolder(name, safeParentId, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'create folder failed'));
            return;
          }
          resolve(result.id);
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 获取文件夹信息（包含子文件夹和文档）
   */
  const getFolder = async (id: number) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    const result = await new Promise<Folder>((resolve, reject) => {
      try {
        db.getFolder(id, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'get folder failed'));
            return;
          }
          resolve(result);
        });
      } catch (err) {
        reject(toError(err));
      }
    });

    // 获取完整的文档信息
    const documents = await Promise.all(
      (result.documents ?? []).map(async (doc) => {
        try {
          return await getDocument(doc.id);
        } catch (err) {
          console.error('get document detail failed:', err);
          return null;
        }
      }),
    ).then((docs) => docs.filter((doc): doc is Document => doc !== null));

    // 构建文件夹结构
    const folder: Folder = {
      id: result.id,
      name: result.name,
      parent_id: result.parent_id ?? null,
      created_at: result.created_at,
      updated_at: result.updated_at,
      sort_order: result.sort_order ?? 0,
      children: result.children ?? [],
      documents: documents,
    };

    return folder;
  };

  /**
   * 更新文件夹
   */
  const updateFolder = async (id: number, name: string, parent_id?: number | null) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<void>((resolve, reject) => {
      try {
        // 使用工具函数确保参数安全
        const safeParentId = validateParams.safeId(parent_id);
        db.updateFolder(id, name, safeParentId, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'update folder failed'));
            return;
          }
          resolve();
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 删除文件夹
   */
  const deleteFolder = async (id: number) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<void>((resolve, reject) => {
      try {
        db.deleteFolder(id, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'delete folder failed'));
            return;
          }
          resolve();
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 获取文件夹列表
   */

  const listFolders = async (parent_id?: number | null) => {
    console.log('🔍 [useSqlite] listFolders 被调用, parent_id:', parent_id);
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<Folder[]>((resolve, reject) => {
      try {
        // 使用后端期望的参数处理方式：null -> -1
        const safeParentId = validateParams.safeId(parent_id);

        db.listFolders(safeParentId, (result) => {
          if (!result) {
            console.error('❌ [useSqlite] 文件夹查询返回空结果');
            resolve([]);
            return;
          }

          if (!result.success) {
            const errorMsg = result.error || 'get folder list failed';
            console.error('❌ [useSqlite] 文件夹查询失败:', errorMsg);
            reject(new Error(errorMsg));
            return;
          }

          // 只要 success 为 true 就认为成功，folders 可以是空数组
          const folders = Array.isArray(result.folders) ? result.folders : [];
          console.log(
            '✅ [useSqlite] 文件夹查询成功, parent_id:',
            parent_id,
            '数量:',
            folders.length,
          );
          resolve(folders);
        });
      } catch (err) {
        console.error('❌ [useSqlite] 文件夹查询异常:', err);
        reject(toError(err));
      }
    });
  };

  // 构建文件夹树结构
  const buildFolderTree = (folders: Folder[], parent_id: number | null = null): FolderTree[] => {
    return folders
      .filter((folder) => folder.parent_id === parent_id)
      .map((folder) => ({
        ...folder,
        children: buildFolderTree(folders, folder.id),
      }));
  };

  // 获取文件夹树
  const getFolderTree = async (parent_id?: number) => {
    const folders = await listFolders(parent_id);
    return buildFolderTree(folders);
  };

  /**
   * 根据 key 获取设置
   */
  const getSetting = async (key: string) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<string>((resolve, reject) => {
      try {
        db.getSetting(key, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'get setting failed'));
            return;
          }
          resolve(result.value);
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 设置一个值
   */
  const setSetting = async (key: string, value: string) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<void>((resolve, reject) => {
      try {
        db.setSetting(key, value, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'updated setting failed'));
            return;
          }
          resolve();
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 通用设置保存方法
   * @param settingsKey 设置键名
   * @param settings 设置对象
   * @param errorMessage 错误消息
   */
  const saveSettings = async <T>(
    settingsKey: string,
    settings: T,
    errorMessage: string,
  ): Promise<void> => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    if (!settings) {
      throw new Error('Settings cannot be null or undefined');
    }

    return new Promise<void>((resolve, reject) => {
      try {
        const settingsJson = JSON.stringify(settings);
        if (settingsKey === 'app_settings') {
          db.setAppSettings(settingsJson, (result: { success: boolean; error?: string }) => {
            if (!result.success) {
              reject(new Error(result.error || errorMessage));
              return;
            }
            resolve();
          });
        } else {
          db.setLlmSettings(settingsJson, (result: { success: boolean; error?: string }) => {
            if (!result.success) {
              reject(new Error(result.error || errorMessage));
              return;
            }
            resolve();
          });
        }
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 获取应用设置
   */
  const getAppSettings = async (): Promise<AppSettings | null> => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<AppSettings | null>((resolve, reject) => {
      try {
        db.getAppSettings((settings: AppSettings | null) => {
          // 现在直接返回settings对象，如果为空则使用null
          if (settings && Object.keys(settings).length > 0) {
            resolve(settings);
          } else {
            resolve(null);
          }
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 保存应用设置
   */
  const setAppSettings = async (settings: AppSettings): Promise<void> => {
    return saveSettings('app_settings', settings, 'save app settings failed');
  };

  /**
   * 获取 LLM 设置
   */
  const getLlmSettings = async (): Promise<LlmSettings | null> => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<LlmSettings | null>((resolve, reject) => {
      try {
        db.getLlmSettings((result) => {
          if (!result.success) {
            reject(new Error(result.error || 'get llm settings failed'));
            return;
          }
          resolve(result.settings || null);
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 保存 LLM 设置
   */
  const setLlmSettings = async (settings: LlmSettings): Promise<void> => {
    return saveSettings('llm_settings', settings, 'save llm settings failed');
  };

  /**
   * 更新文件夹排序
   */
  const updateFolderOrder = async (parent_id: number | null, folder_ids: number[]) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');
    console.log('🔄 [useSqlite] updateFolderOrder called with:', { parent_id, folder_ids });
    console.log(
      '🔄 [useSqlite] folder_ids type:',
      typeof folder_ids,
      'isArray:',
      Array.isArray(folder_ids),
    );

    try {
      // 使用工具函数确保参数安全
      const safeParentId = validateParams.safeId(parent_id);
      console.log('🔄 [useSqlite] Calling Qt method with:', { safeParentId, folder_ids });

      const resultJson = db.reorderFolders(safeParentId, folder_ids);
      console.log('🔄 [useSqlite] Qt method returned:', resultJson);
      console.log('🔄 [useSqlite] Result type:', typeof resultJson);

      // 检查返回值类型并处理
      let finalResult: string;

      if (typeof resultJson === 'string') {
        finalResult = resultJson;
      } else if (
        resultJson &&
        typeof (resultJson as unknown as Promise<string>).then === 'function'
      ) {
        // 如果返回的是 Promise，等待它完成
        console.log('🔄 [useSqlite] Detected Promise, awaiting...');
        finalResult = await (resultJson as Promise<string>);
        console.log('🔄 [useSqlite] Promise resolved:', finalResult);
      } else {
        console.error('❌ [useSqlite] Unexpected return type:', typeof resultJson, resultJson);
        throw new Error('Unexpected return type from Qt method');
      }

      const result = JSON.parse(finalResult);
      if (!result.success) {
        console.error('❌ [useSqlite] updateFolderOrder failed:', result.error);
        throw new Error(result.error || 'update folder order failed');
      }

      console.log('✅ [useSqlite] updateFolderOrder succeeded');
    } catch (err) {
      console.error('❌ [useSqlite] updateFolderOrder exception:', err);
      throw toError(err);
    }
  };

  /**
   * 更新文档排序
   */
  const updateDocumentOrder = async (folder_id: number, document_ids: number[]) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');
    console.log('🔄 [useSqlite] updateDocumentOrder called with:', { folder_id, document_ids });
    console.log(
      '🔄 [useSqlite] document_ids type:',
      typeof document_ids,
      'isArray:',
      Array.isArray(document_ids),
    );

    try {
      // 使用工具函数确保参数安全
      const safeFolderId = validateParams.safeNumber(folder_id, -1);
      console.log('🔄 [useSqlite] Calling Qt method with:', { safeFolderId, document_ids });

      const resultJson = db.reorderDocuments(safeFolderId, document_ids);
      console.log('🔄 [useSqlite] Qt method returned:', resultJson);
      console.log('🔄 [useSqlite] Result type:', typeof resultJson);

      // 检查返回值类型并处理
      let finalResult: string;

      if (typeof resultJson === 'string') {
        finalResult = resultJson;
      } else if (
        resultJson &&
        typeof (resultJson as unknown as Promise<string>).then === 'function'
      ) {
        // 如果返回的是 Promise，等待它完成
        console.log('🔄 [useSqlite] Detected Promise, awaiting...');
        finalResult = await (resultJson as Promise<string>);
        console.log('🔄 [useSqlite] Promise resolved:', finalResult);
      } else {
        console.error('❌ [useSqlite] Unexpected return type:', typeof resultJson, resultJson);
        throw new Error('Unexpected return type from Qt method');
      }

      const result = JSON.parse(finalResult);
      if (!result.success) {
        console.error('❌ [useSqlite] updateDocumentOrder failed:', result.error);
        throw new Error(result.error || 'update document order failed');
      }

      console.log('✅ [useSqlite] updateDocumentOrder succeeded');
    } catch (err) {
      console.error('❌ [useSqlite] updateDocumentOrder exception:', err);
      throw toError(err);
    }
  };

  /**
   * 保存图片
   */
  const saveImage = async (document_id: number, imageData: string, mimeType: string) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<number>((resolve, reject) => {
      try {
        // 只保留 base64 字符串，后端需转为二进制
        const pureBase64 = imageData.split(',')[1];
        db.saveImage(document_id, pureBase64, mimeType, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'save image failed'));
            return;
          }
          resolve(result.id);
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 获取图片
   */
  const getImage = async (image_id: number) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<{
      file_url?: string;
      data?: string;
      mime_type: string;
      path?: string;
      full_path?: string;
    }>((resolve, reject) => {
      try {
        db.getImage(image_id, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'get image failed'));
            return;
          }
          resolve({
            file_url: result.file_url,
            data: result.data, // 保持向后兼容
            mime_type: result.mime_type || '',
            path: result.path,
            full_path: result.full_path,
          });
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 检查图片引用
   */
  const checkImageReferences = async (image_id: number) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');
    return new Promise<{
      hasReferences: boolean;
      referencingDocuments: Array<{ id: number; title: string }>;
    }>((resolve, reject) => {
      try {
        db.checkImageReferences(image_id, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'check image references failed'));
            return;
          }
          resolve({
            hasReferences: result.hasReferences || false,
            referencingDocuments: result.referencingDocuments || [],
          });
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 删除图片（会自动检查引用）
   */
  const deleteImage = async (image_id: number, current_document_id: number = -1) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    console.log('useSqlite deleteImage 调用参数:', { image_id, current_document_id });

    return new Promise<void>((resolve, reject) => {
      try {
        db.deleteImage(image_id, current_document_id, (result) => {
          console.log('deleteImage result:', result);
          if (!result.success) {
            reject(new Error(result.error || 'delete image failed'));
            return;
          }
          resolve();
        });
      } catch (err) {
        console.error('deleteImage error:', err);
        reject(toError(err));
      }
    });
  };

  /**
   * 创建对话
   * @param document_id 关联的文档ID，传入-1表示不关联任何文档
   * @param title 对话标题
   * @param messages 消息JSON字符串
   * @param prompt 提示词
   */
  const createConversation = async (
    document_id: number,
    title: string,
    messages: string,
    prompt: string,
  ) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<number>((resolve, reject) => {
      try {
        // 使用工具函数确保参数安全，独立对话使用-1
        const safeDocumentId = validateParams.safeNumber(document_id, -1);

        db.createConversation(safeDocumentId, title, messages, prompt, (result) => {
          console.log('createConversation params:', safeDocumentId, title, messages, prompt);
          if (!result.success) {
            reject(new Error(result.error || 'create conversation failed'));
            return;
          }
          resolve(result.id);
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 获取对话
   */
  const getConversation = async (id: number) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<Conversation>((resolve, reject) => {
      try {
        db.getConversation(id, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'get conversation failed'));
            return;
          }
          // 解析 messages JSON
          resolve(result);
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 更新对话
   */
  const updateConversation = async (
    id: number,
    title: string,
    messages: string,
    prompt: string,
  ) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<void>((resolve, reject) => {
      try {
        db.updateConversation(id, title, messages, prompt, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'update conversation failed'));
            return;
          }
          resolve();
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 删除对话
   */
  const deleteConversation = async (id: number) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<void>((resolve, reject) => {
      try {
        db.deleteConversation(id, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'delete conversation failed'));
            return;
          }
          resolve();
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 获取文档的所有对话
   */
  const listConversations = async (document_id: number) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<Conversation[]>((resolve, reject) => {
      try {
        // 确保数据库已初始化并且API可用
        if (!db || typeof db.listConversations !== 'function') {
          console.warn('⚠️ [useSqlite] 数据库API未初始化或listConversations方法不可用');
          resolve([]);
          return;
        }

        // 使用工具函数确保参数安全
        const safeDocumentId = validateParams.safeNumber(document_id, -1);

        db.listConversations(safeDocumentId, (result) => {
          if (!result) {
            console.warn('⚠️ [useSqlite] listConversations return empty result');
            resolve([]);
            return;
          }

          if (!result.success) {
            console.error('❌ [useSqlite] listConversations failed:', result.error);
            reject(new Error(result.error || 'get conversation list failed'));
            return;
          }

          if (!result.conversations || !Array.isArray(result.conversations)) {
            resolve([]);
          } else {
            // 直接使用 messages，因为它已经是 Message[] 类型
            const conversations = result.conversations.map((conv: Conversation) => ({
              ...conv,
              messages: conv.messages,
            }));
            resolve(conversations);
          }
        });
      } catch (err) {
        console.error('❌ [useSqlite] listConversations error:', err);
        reject(toError(err));
      }
    });
  };

  /**
   * 获取所有未关联文档的对话（独立对话）
   */
  const listChatConversations = async () => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<Conversation[]>((resolve, reject) => {
      try {
        // 确保数据库已初始化并且API可用
        if (!db || typeof db.listChatConversations !== 'function') {
          console.warn('⚠️ [useSqlite] 数据库API未初始化或listChatConversations方法不可用');
          resolve([]);
          return;
        }

        db.listChatConversations((result) => {
          if (!result) {
            console.warn('⚠️ [useSqlite] listChatConversations return empty result');
            resolve([]);
            return;
          }

          if (!result.success) {
            console.error('❌ [useSqlite] listChatConversations failed:', result.error);
            reject(new Error(result.error || 'get conversation list failed'));
            return;
          }

          if (!result.conversations || !Array.isArray(result.conversations)) {
            resolve([]);
          } else {
            const conversations = result.conversations.map((conv: Conversation) => ({
              ...conv,
              messages: conv.messages,
            }));
            resolve(conversations);
          }
        });
      } catch (err) {
        console.error('❌ [useSqlite] listChatConversations error:', err);
        reject(toError(err));
      }
    });
  };

  /**
   * 创建文档快照
   */
  const createSnapshot = async (document_id: number, snapshot_name: string) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<{
      name: string;
      content: string;
      created_at: string;
    }>((resolve, reject) => {
      try {
        db.createSnapshot(document_id, snapshot_name, (result) => {
          if (!result.success) {
            reject(new Error(result.error || 'create snapshot failed'));
            return;
          }
          if (!result.snapshot) {
            reject(new Error('create snapshot failed: return data is empty'));
            return;
          }
          resolve(result.snapshot);
        });
      } catch (err) {
        reject(toError(err));
      }
    });
  };

  /**
   * 根据关键词搜索文件夹
   */
  const searchFolders = async (keyword: string) => {
    await initialize();
    if (!db) throw new Error('Database not initialized');

    return new Promise<{
      success: boolean;
      data: Array<{
        id: number;
        name: string;
        parent_id: number | null;
        parent_name?: string;
        document_count: number;
      }>;
      message?: string;
    }>((resolve, reject) => {
      try {
        // 检查方法是否存在
        if (typeof db.searchFolders !== 'function') {
          console.warn('⚠️ [useSqlite] searchFolders 方法不存在，可能需要重新编译Qt项目');
          resolve({
            success: false,
            data: [],
            message: 'API方法不可用，请重新编译Qt项目',
          });
          return;
        }

        console.log('🔍 [useSqlite] 搜索文件夹, keyword:', keyword);

        // 调用同步方法获取结果
        const resultJson = db.searchFolders(keyword);
        let finalResult: string;

        if (typeof resultJson === 'string') {
          finalResult = resultJson;
        } else {
          console.error('❌ [useSqlite] Unexpected return type:', typeof resultJson);
          reject(new Error('Unexpected return type from Qt method'));
          return;
        }

        const result = JSON.parse(finalResult);

        if (!result.success) {
          console.error('❌ [useSqlite] searchFolders failed:', result.error);
          reject(new Error(result.error || 'search folders failed'));
          return;
        }

        console.log('✅ [useSqlite] searchFolders succeeded, count:', result.data?.length || 0);

        // 确保返回的数据格式正确
        const searchResults = Array.isArray(result.data)
          ? result.data.map(
              (item: {
                id: number;
                name: string;
                parent_id: number | null;
                parent_name?: string;
                document_count: number;
              }) => ({
                id: Number(item.id),
                name: String(item.name || ''),
                parent_id: item.parent_id !== null ? Number(item.parent_id) : null,
                parent_name: item.parent_name ? String(item.parent_name) : undefined,
                document_count: Number(item.document_count || 0),
              }),
            )
          : [];

        resolve({
          success: true,
          data: searchResults,
        });
      } catch (err) {
        console.error('❌ [useSqlite] searchFolders error:', err);
        reject(toError(err));
      }
    });
  };

  /**
   * 搜索文档
   */
  const searchDocuments = (
    searchText: string,
    searchInContent: boolean = false,
    folderId?: number,
  ): Promise<{
    success: boolean;
    data: Array<{
      id: number;
      title: string;
      folder_id: number | null;
      folder_name: string;
      match_type: 'title' | 'content';
      preview?: string;
      created_at: string;
      updated_at: string;
    }>;
    message?: string;
  }> => {
    return new Promise<{
      success: boolean;
      data: Array<{
        id: number;
        title: string;
        folder_id: number | null;
        folder_name: string;
        match_type: 'title' | 'content';
        preview?: string;
        created_at: string;
        updated_at: string;
      }>;
      message?: string;
    }>((resolve, reject) => {
      try {
        // 检查方法是否存在
        if (typeof db.searchDocuments !== 'function') {
          console.warn('⚠️ [useSqlite] searchDocuments 方法不存在，可能需要重新编译Qt项目');
          resolve({
            success: false,
            data: [],
            message: 'API方法不可用，请重新编译Qt项目',
          });
          return;
        }

        console.log(
          '🔍 [useSqlite] 搜索文档, searchText:',
          searchText,
          'searchInContent:',
          searchInContent,
          'folderId:',
          folderId,
        );

        // 调用方法获取结果（可能是同步或异步）
        const resultJson: unknown = db.searchDocuments(searchText, searchInContent, folderId || -1);
        console.log('🔍 [useSqlite] 搜索文档结果:::::::::::::', resultJson);

        // 处理Promise或直接字符串结果
        const handleResult = (finalResult: string) => {
          try {
            const result = JSON.parse(finalResult);

            if (!result.success) {
              console.error('❌ [useSqlite] searchDocuments failed:', result.error);
              reject(new Error(result.error || 'search documents failed'));
              return;
            }

            console.log(
              '✅ [useSqlite] searchDocuments succeeded, count:',
              result.data?.length || 0,
            );

            // 确保返回的数据格式正确
            const searchResults = Array.isArray(result.data)
              ? result.data.map(
                  (item: {
                    id: number;
                    title: string;
                    folder_id: number | null;
                    folder_name: string;
                    match_type: string;
                    preview?: string;
                    created_at: string;
                    updated_at: string;
                  }) => ({
                    id: Number(item.id),
                    title: String(item.title || ''),
                    folder_id: item.folder_id !== null ? Number(item.folder_id) : null,
                    folder_name: String(item.folder_name || ''),
                    match_type: item.match_type === 'content' ? 'content' : 'title',
                    preview: item.preview ? String(item.preview) : undefined,
                    created_at: String(item.created_at || ''),
                    updated_at: String(item.updated_at || ''),
                  }),
                )
              : [];

            resolve({
              success: true,
              data: searchResults,
              message: result.message,
            });
          } catch (parseError) {
            console.error('❌ [useSqlite] JSON parse error:', parseError);
            reject(new Error('Failed to parse search results'));
          }
        };

        // 检查返回类型并相应处理
        if (typeof resultJson === 'string') {
          // 同步结果
          handleResult(resultJson);
        } else if (
          resultJson &&
          typeof (resultJson as Record<string, unknown>).then === 'function'
        ) {
          // Promise结果
          (resultJson as Promise<string>)
            .then((result: string) => {
              console.log('🔍 [useSqlite] Promise resolved with:', typeof result);
              if (typeof result === 'string') {
                handleResult(result);
              } else {
                console.error('❌ [useSqlite] Promise resolved with non-string:', typeof result);
                reject(new Error('Promise resolved with unexpected type'));
              }
            })
            .catch((error: Error) => {
              console.error('❌ [useSqlite] Promise rejected:', error);
              reject(new Error('Search operation failed: ' + error.message));
            });
        } else {
          console.error('❌ [useSqlite] Unexpected return type:', typeof resultJson);
          reject(new Error('Unexpected return type from Qt method'));
        }
      } catch (err) {
        console.error('❌ [useSqlite] searchDocuments error:', err);
        reject(toError(err));
      }
    });
  };

  // ==================== 图片相关方法 ====================

  const deleteDocumentImages = (_documentId: number, callback?: () => void): Promise<void> => {
    return new Promise((resolve) => {
      if (!window.databaseApi) {
        console.error('DatabaseApi not available');
        if (callback) callback();
        resolve();
        return;
      }

      // 调用后端的deleteDocumentImages方法
      // 这个方法会移除文档的所有图片引用并清理无引用的图片
      try {
        // 由于后端已经重构，这里直接调用新的引用清理逻辑
        window.databaseApi.deleteUnreferencedImages(
          (result: { success: boolean; error?: string }) => {
            if (!result.success) {
              console.error('clean up unreferenced images failed:', result.error);
            }
            if (callback) callback();
            resolve();
          },
        );
      } catch (error) {
        console.error('delete document images failed:', error);
        if (callback) callback();
        resolve();
      }
    });
  };

  // ==================== 知识库相关方法 ====================

  // ==================== 知识库功能已迁移到ObjectBox ====================
  // 所有知识库相关功能已迁移到 KnowledgeApi (ObjectBox)
  // useSqlite 现在只负责用户文档、文件夹、设置、对话等功能

  return {
    isInitialized: readonly(isInitialized),
    initialize,
    createDocument,
    getDocument,
    getAllDocumentsInFolder,
    updateDocument,
    renameDocument,
    addDocumentKnowledgeAssociation,
    removeDocumentKnowledgeAssociation,
    getDocumentKnowledgeAssociations,
    getOriginalDocumentByKnowledgeDocumentId,
    isDocumentInKnowledgeBase,
    checkKnowledgeDocumentNeedsRevectorization,
    clearDocumentKnowledgeAssociation,
    clearKnowledgeBaseAssociations,
    updateKnowledgeDocumentAssociationTime,
    deleteDocument,
    createFolder,
    getFolder,
    updateFolder,
    deleteFolder,
    listFolders,
    getFolderTree,
    getSetting,
    setSetting,
    updateFolderOrder,
    updateDocumentOrder,
    getAppSettings,
    setAppSettings,
    getLlmSettings,
    setLlmSettings,
    saveImage,
    getImage,
    checkImageReferences,
    deleteImage,
    deleteDocumentImages,
    createConversation,
    getConversation,
    updateConversation,
    deleteConversation,
    listConversations,
    listChatConversations,
    createSnapshot,
    searchFolders,
    searchDocuments,
    // 知识库功能已迁移到 KnowledgeApi (ObjectBox)
  };
}

export interface FolderTree extends Folder {
  children: FolderTree[];
}
